// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.0
// source: dict_category.proto

package dict_category

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DictCategoryService_CreateDictCategory_FullMethodName = "/dict_category.DictCategoryService/CreateDictCategory"
	DictCategoryService_UpdateDictCategory_FullMethodName = "/dict_category.DictCategoryService/UpdateDictCategory"
	DictCategoryService_DeleteDictCategory_FullMethodName = "/dict_category.DictCategoryService/DeleteDictCategory"
	DictCategoryService_GetDictCategory_FullMethodName    = "/dict_category.DictCategoryService/GetDictCategory"
	DictCategoryService_ListDictCategory_FullMethodName   = "/dict_category.DictCategoryService/ListDictCategory"
)

// DictCategoryServiceClient is the client API for DictCategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 字典分类服务
type DictCategoryServiceClient interface {
	// 创建字典分类
	CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error)
	// 更新字典分类
	UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error)
	// 删除字典分类
	DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error)
	// 获取字典分类详情
	GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error)
	// 字典分类列表
	ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error)
}

type dictCategoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDictCategoryServiceClient(cc grpc.ClientConnInterface) DictCategoryServiceClient {
	return &dictCategoryServiceClient{cc}
}

func (c *dictCategoryServiceClient) CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDictCategoryResp)
	err := c.cc.Invoke(ctx, DictCategoryService_CreateDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictCategoryServiceClient) UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDictCategoryResp)
	err := c.cc.Invoke(ctx, DictCategoryService_UpdateDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictCategoryServiceClient) DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDictCategoryResp)
	err := c.cc.Invoke(ctx, DictCategoryService_DeleteDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictCategoryServiceClient) GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDictCategoryResp)
	err := c.cc.Invoke(ctx, DictCategoryService_GetDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictCategoryServiceClient) ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDictCategoryResp)
	err := c.cc.Invoke(ctx, DictCategoryService_ListDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DictCategoryServiceServer is the server API for DictCategoryService service.
// All implementations must embed UnimplementedDictCategoryServiceServer
// for forward compatibility.
//
// 字典分类服务
type DictCategoryServiceServer interface {
	// 创建字典分类
	CreateDictCategory(context.Context, *CreateDictCategoryReq) (*CreateDictCategoryResp, error)
	// 更新字典分类
	UpdateDictCategory(context.Context, *UpdateDictCategoryReq) (*UpdateDictCategoryResp, error)
	// 删除字典分类
	DeleteDictCategory(context.Context, *DeleteDictCategoryReq) (*DeleteDictCategoryResp, error)
	// 获取字典分类详情
	GetDictCategory(context.Context, *GetDictCategoryReq) (*GetDictCategoryResp, error)
	// 字典分类列表
	ListDictCategory(context.Context, *ListDictCategoryReq) (*ListDictCategoryResp, error)
	mustEmbedUnimplementedDictCategoryServiceServer()
}

// UnimplementedDictCategoryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDictCategoryServiceServer struct{}

func (UnimplementedDictCategoryServiceServer) CreateDictCategory(context.Context, *CreateDictCategoryReq) (*CreateDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDictCategory not implemented")
}
func (UnimplementedDictCategoryServiceServer) UpdateDictCategory(context.Context, *UpdateDictCategoryReq) (*UpdateDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDictCategory not implemented")
}
func (UnimplementedDictCategoryServiceServer) DeleteDictCategory(context.Context, *DeleteDictCategoryReq) (*DeleteDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDictCategory not implemented")
}
func (UnimplementedDictCategoryServiceServer) GetDictCategory(context.Context, *GetDictCategoryReq) (*GetDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDictCategory not implemented")
}
func (UnimplementedDictCategoryServiceServer) ListDictCategory(context.Context, *ListDictCategoryReq) (*ListDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDictCategory not implemented")
}
func (UnimplementedDictCategoryServiceServer) mustEmbedUnimplementedDictCategoryServiceServer() {}
func (UnimplementedDictCategoryServiceServer) testEmbeddedByValue()                             {}

// UnsafeDictCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DictCategoryServiceServer will
// result in compilation errors.
type UnsafeDictCategoryServiceServer interface {
	mustEmbedUnimplementedDictCategoryServiceServer()
}

func RegisterDictCategoryServiceServer(s grpc.ServiceRegistrar, srv DictCategoryServiceServer) {
	// If the following call pancis, it indicates UnimplementedDictCategoryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DictCategoryService_ServiceDesc, srv)
}

func _DictCategoryService_CreateDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictCategoryServiceServer).CreateDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictCategoryService_CreateDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictCategoryServiceServer).CreateDictCategory(ctx, req.(*CreateDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictCategoryService_UpdateDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictCategoryServiceServer).UpdateDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictCategoryService_UpdateDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictCategoryServiceServer).UpdateDictCategory(ctx, req.(*UpdateDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictCategoryService_DeleteDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictCategoryServiceServer).DeleteDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictCategoryService_DeleteDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictCategoryServiceServer).DeleteDictCategory(ctx, req.(*DeleteDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictCategoryService_GetDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictCategoryServiceServer).GetDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictCategoryService_GetDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictCategoryServiceServer).GetDictCategory(ctx, req.(*GetDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictCategoryService_ListDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictCategoryServiceServer).ListDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictCategoryService_ListDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictCategoryServiceServer).ListDictCategory(ctx, req.(*ListDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DictCategoryService_ServiceDesc is the grpc.ServiceDesc for DictCategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DictCategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dict_category.DictCategoryService",
	HandlerType: (*DictCategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDictCategory",
			Handler:    _DictCategoryService_CreateDictCategory_Handler,
		},
		{
			MethodName: "UpdateDictCategory",
			Handler:    _DictCategoryService_UpdateDictCategory_Handler,
		},
		{
			MethodName: "DeleteDictCategory",
			Handler:    _DictCategoryService_DeleteDictCategory_Handler,
		},
		{
			MethodName: "GetDictCategory",
			Handler:    _DictCategoryService_GetDictCategory_Handler,
		},
		{
			MethodName: "ListDictCategory",
			Handler:    _DictCategoryService_ListDictCategory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dict_category.proto",
}
