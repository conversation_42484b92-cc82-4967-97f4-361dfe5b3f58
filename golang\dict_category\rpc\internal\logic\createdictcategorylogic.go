package logic

import (
	"context"
	"database/sql"
	"time"

	"golang-docker-compose-main/golang/dict_category/rpc/dict_category"
	"golang-docker-compose-main/golang/dict_category/rpc/internal/svc"
	"golang-docker-compose-main/golang/dict_category/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典分类
func (l *CreateDictCategoryLogic) CreateDictCategory(in *dict_category.CreateDictCategoryReq) (*dict_category.CreateDictCategoryResp, error) {
	// 数据库插入操作
	now := time.Now()
	result, err := l.svcCtx.DictCategoryModel.Insert(l.ctx, &model.DictCategory{
		DictId:      in.DictId,
		TenantId:    in.TenantId,
		Name:        in.Name,
		Sort:        float64(in.Sort),
		Status:      int64(in.Status),
		CreatedBy:   sql.NullInt64{Int64: in.CreatedBy, Valid: in.CreatedBy > 0},
		CreatedTime: sql.NullTime{Time: now, Valid: true},
	})
	if err != nil {
		return nil, err
	}

	// 获取插入ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &dict_category.CreateDictCategoryResp{
		Id: id,
	}, nil
}
