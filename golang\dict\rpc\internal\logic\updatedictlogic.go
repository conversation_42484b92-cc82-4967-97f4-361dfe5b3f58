package logic

import (
	"context"
	"dict/rpc/dict"
	"dict/rpc/internal/svc"
	"fmt"
	"mp/rpc/model"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictLogic {
	return &UpdateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典
func (l *UpdateDictLogic) UpdateDict(in *dict.UpdateDictReq) (*dict.UpdateDictResp, error) {
	// 添加日志
	l.Infof("接收到更新字典请求: %+v", in)

	// 检查DB是否为nil
	if l.svcCtx.DB == nil {
		return nil, fmt.Errorf("数据库连接为空，请检查数据库配置")
	}

	// 检查ID是否有效
	if in.Id <= 0 {
		return nil, fmt.Errorf("无效的ID: %d", in.Id)
	}

	// 查询记录是否存在
	var dictData model.Dicts
	result := l.svcCtx.DB.First(&dictData, in.Id)
	if result.Error != nil {
		l.Errorf("查询字典失败: %v", result.Error)
		return nil, fmt.Errorf("字典不存在或查询失败: %v", result.Error)
	}

	// 如果提供了Code，需要验证格式
	var Code int
	var err error
	if in.Code != "" {
		Code, err = strconv.Atoi(in.Code)
		if err != nil {
			return nil, fmt.Errorf("编码格式错误: %v", err)
		}
	}

	// 构建更新数据
	updateData := map[string]interface{}{}

	// 只更新提供的字段
	if in.TenantId > 0 {
		updateData["tenantId"] = uint(in.TenantId)
	}
	if in.CategoryId > 0 {
		updateData["category_id"] = uint(in.CategoryId)
	}
	if in.Code != "" {
		updateData["code"] = Code
	}
	if in.Name != "" {
		updateData["name"] = in.Name
	}
	if in.ShowType != "" {
		updateData["show_type"] = in.ShowType
	}
	if in.Remark != "" {
		updateData["remark"] = in.Remark
	}
	if in.Status > 0 {
		updateData["status"] = in.Status
	}
	if in.UpdatedBy > 0 {
		updateData["updated_by"] = uint(in.UpdatedBy)
	}

	// 更新时间
	updateData["updated_time"] = time.Now()

	// 执行更新
	l.Infof("更新数据: %+v", updateData)
	result = l.svcCtx.DB.Model(&dictData).Updates(updateData)
	if result.Error != nil {
		l.Errorf("更新字典失败: %v", result.Error)
		return nil, fmt.Errorf("更新字典失败: %v", result.Error)
	}

	l.Infof("更新字典成功，ID: %d", in.Id)
	return &dict.UpdateDictResp{
		Success: true,
	}, nil
}
