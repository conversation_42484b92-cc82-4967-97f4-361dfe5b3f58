syntax = "proto3";

package dict_item;
option go_package="./dict_item";

// 字典项基础信息
message DictItem {
  int64 id = 1;
  int64 tenant_id = 2;
  int64 dict_id = 3;
  int64 category_id = 4;
  string code = 5;
  string name = 6;
  int64 pid = 7;
  double sort = 8;
  int32 status = 9;
  int64 created_by = 10;
  string created_time = 11;
  string updated_time = 12;
}

// 创建字典项请求
message CreateDictItemReq {
  int64 tenant_id = 1;
  int64 dict_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  int64 pid = 6;
  double sort = 7;
  int32 status = 8;
  int64 created_by = 9;
}

// 创建字典项响应
message CreateDictItemResp {
  int64 id = 1;
}

// 更新字典项请求
message UpdateDictItemReq {
  int64 id = 1;
  int64 tenant_id = 2;
  int64 dict_id = 3;
  int64 category_id = 4;
  string code = 5;
  string name = 6;
  int64 pid = 7;
  double sort = 8;
  int32 status = 9;
  int64 updated_by = 10;
}

// 更新字典项响应
message UpdateDictItemResp {
  bool success = 1;
}

// 删除字典项请求
message DeleteDictItemReq {
  int64 id = 1;
  int64 deleted_by = 2;
}

// 删除字典项响应
message DeleteDictItemResp {
  bool success = 1;
}

// 获取字典项详情请求
message GetDictItemReq {
  int64 id = 1;
}

// 获取字典项详情响应
message GetDictItemResp {
  DictItem item = 1;
}

// 字典项列表请求
message ListDictItemReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 dict_id = 3;
  int64 category_id = 4;
  string code = 5;
  string name = 6;
  int32 status = 7;
}

// 字典项列表响应
message ListDictItemResp {
  int64 total = 1;
  repeated DictItem list = 2;
}

// 字典项服务
service DictItemService {
  // 创建字典项
  rpc CreateDictItem(CreateDictItemReq) returns (CreateDictItemResp);
  
  // 更新字典项
  rpc UpdateDictItem(UpdateDictItemReq) returns (UpdateDictItemResp);
  
  // 删除字典项
  rpc DeleteDictItem(DeleteDictItemReq) returns (DeleteDictItemResp);
  
  // 获取字典项详情
  rpc GetDictItem(GetDictItemReq) returns (GetDictItemResp);
  
  // 获取字典项列表
  rpc ListDictItem(ListDictItemReq) returns (ListDictItemResp);
} 