package logic

import (
	"context"
	"dict/rpc/dict"
	"dict/rpc/internal/svc"
	"fmt"
	"mp/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictLogic {
	return &DeleteDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典
func (l *DeleteDictLogic) DeleteDict(in *dict.DeleteDictReq) (*dict.DeleteDictResp, error) {
	// 先查询是否存在该记录
	var existingDict model.Dicts
	result := l.svcCtx.DB.First(&existingDict, in.Id)
	if result.Error != nil {
		return nil, fmt.Errorf("查询数据失败: %v", result.Error)
	}

	// 如果没有找到记录
	if result.RowsAffected == 0 {
		return &dict.DeleteDictResp{
			Success: false,
		}, fmt.Errorf("未找到ID为%d的字典记录", in.Id)
	}

	// 执行删除操作
	result = l.svcCtx.DB.Delete(&existingDict)
	if result.Error != nil {
		return &dict.DeleteDictResp{
			Success: false,
		}, fmt.Errorf("删除字典失败: %v", result.Error)
	}

	return &dict.DeleteDictResp{
		Success: true,
	}, nil
}
