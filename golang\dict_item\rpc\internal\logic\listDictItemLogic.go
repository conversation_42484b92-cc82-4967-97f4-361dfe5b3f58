package logic

import (
	"context"

	"dict_item/rpc/dict_item"
	"dict_item/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictItemLogic {
	return &ListDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典项列表 (只查询未删除的记录)
func (l *ListDictItemLogic) ListDictItem(in *dict_item.ListDictItemReq) (*dict_item.ListDictItemResp, error) {
	// 查询列表，FindList方法已经包含了只查询deleted_time为NULL的记录
	items, total, err := l.svcCtx.DictItemModel.FindList(
		l.ctx,
		int(in.Page),
		int(in.PageSize),
		in.DictId,
		in.CategoryId,
		in.Code,
		in.Name,
		int(in.Status),
	)
	if err != nil {
		return nil, err
	}

	// 构建响应
	list := make([]*dict_item.DictItem, 0, len(items))
	for _, item := range items {
		dictItem := &dict_item.DictItem{
			Id:     item.Id,
			DictId: item.DictId,
			Code:   item.Code,
			Name:   item.Name,
			Sort:   item.Sort,
			Status: int32(item.Status),
		}

		// 处理可空字段
		if item.TenantId.Valid {
			dictItem.TenantId = item.TenantId.Int64
		}
		if item.CategoryId.Valid {
			dictItem.CategoryId = item.CategoryId.Int64
		}
		if item.Pid.Valid {
			dictItem.Pid = item.Pid.Int64
		}
		if item.CreatedBy.Valid {
			dictItem.CreatedBy = item.CreatedBy.Int64
		}
		if item.CreatedTime.Valid {
			dictItem.CreatedTime = item.CreatedTime.Time.Format("2006-01-02 15:04:05")
		}
		if item.UpdatedTime.Valid {
			dictItem.UpdatedTime = item.UpdatedTime.Time.Format("2006-01-02 15:04:05")
		}

		list = append(list, dictItem)
	}

	return &dict_item.ListDictItemResp{
		Total: total,
		List:  list,
	}, nil
}
