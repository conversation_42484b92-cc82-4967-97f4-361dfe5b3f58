// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	dictItemFieldNames          = builder.RawFieldNames(&DictItem{})
	dictItemRows                = strings.Join(dictItemFieldNames, ",")
	dictItemRowsExpectAutoSet   = strings.Join(stringx.Remove(dictItemFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	dictItemRowsWithPlaceHolder = strings.Join(stringx.Remove(dictItemFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheDictItemIdPrefix = "cache:dictItem:id:"
)

type (
	dictItemModel interface {
		Insert(ctx context.Context, data *DictItem) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*DictItem, error)
		Update(ctx context.Context, data *DictItem) error
		Delete(ctx context.Context, id int64) error
	}

	defaultDictItemModel struct {
		sqlc.CachedConn
		table string
	}

	DictItem struct {
		Id          int64         `db:"id"`
		TenantId    sql.NullInt64 `db:"tenant_id"`    // 归属租户
		DictId      int64         `db:"dict_id"`      // 归属字典
		CategoryId  sql.NullInt64 `db:"category_id"`  // 归属分类
		Code        string        `db:"code"`         // 编码
		Name        string        `db:"name"`         // 显示名称
		Pid         sql.NullInt64 `db:"pid"`          // 所属上级
		Sort        float64       `db:"sort"`         // 排序
		Status      int64         `db:"status"`       // 数据状态
		CreatedBy   sql.NullInt64 `db:"created_by"`   // 添加人
		UpdatedBy   sql.NullInt64 `db:"updated_by"`   // 编辑人
		DeletedBy   sql.NullInt64 `db:"deleted_by"`   // 删除人
		CreatedTime sql.NullTime  `db:"created_time"` // 添加时间
		UpdatedTime sql.NullTime  `db:"updated_time"` // 更新时间
		DeletedTime sql.NullTime  `db:"deleted_time"` // 删除时间
	}
)

func newDictItemModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultDictItemModel {
	return &defaultDictItemModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`dict_item`",
	}
}

func (m *defaultDictItemModel) Delete(ctx context.Context, id int64) error {
	dictItemIdKey := fmt.Sprintf("%s%v", cacheDictItemIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, dictItemIdKey)
	return err
}

func (m *defaultDictItemModel) FindOne(ctx context.Context, id int64) (*DictItem, error) {
	dictItemIdKey := fmt.Sprintf("%s%v", cacheDictItemIdPrefix, id)
	var resp DictItem
	err := m.QueryRowCtx(ctx, &resp, dictItemIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", dictItemRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultDictItemModel) Insert(ctx context.Context, data *DictItem) (sql.Result, error) {
	dictItemIdKey := fmt.Sprintf("%s%v", cacheDictItemIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, dictItemRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.TenantId, data.DictId, data.CategoryId, data.Code, data.Name, data.Pid, data.Sort, data.Status, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.CreatedTime, data.UpdatedTime, data.DeletedTime)
	}, dictItemIdKey)
	return ret, err
}

func (m *defaultDictItemModel) Update(ctx context.Context, data *DictItem) error {
	dictItemIdKey := fmt.Sprintf("%s%v", cacheDictItemIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, dictItemRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.TenantId, data.DictId, data.CategoryId, data.Code, data.Name, data.Pid, data.Sort, data.Status, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.CreatedTime, data.UpdatedTime, data.DeletedTime, data.Id)
	}, dictItemIdKey)
	return err
}

func (m *defaultDictItemModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheDictItemIdPrefix, primary)
}

func (m *defaultDictItemModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", dictItemRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultDictItemModel) tableName() string {
	return m.table
}
