package logic

import (
	"context"
	"dict/api/middleware/JWT"
	"fmt"
	"time"

	"dict/api/internal/svc"
	"dict/api/internal/types"
	"dict/rpc/dictservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictLogic {
	return &DeleteDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictLogic) DeleteDict(req *types.DeleteDictReq) (resp *types.DeleteDictResp, err error) {
	// 调用RPC服务删除字典
	rpcResp, err := l.svcCtx.DictRpc.DeleteDict(l.ctx, &dictservice.DeleteDictReq{
		Id:        req.Id,
		DeletedBy: req.DeletedBy,
	})
	if err != nil {
		return nil, fmt.Errorf("调用用户RPC服务的DeleteDict失败：%v", err)
	}

	// 从配置文件中获取secret 、expire
	secret := l.svcCtx.Config.Auth.AccessSecret
	expire := l.svcCtx.Config.Auth.AccessExpire
	// 生成token
	token, err := JWT.GetJwtToken(secret, time.Now().Unix(), expire, int(req.Id))

	if err != nil {
		logx.Error("token生成失败:", err)
		return nil, err
	}
	return &types.DeleteDictResp{
		Success: rpcResp.Success,
		Base: types.Base{
			Code:  200,
			Msg:   "删除成功",
			Token: token,
		},
	}, nil

}
