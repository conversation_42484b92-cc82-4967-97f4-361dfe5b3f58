// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: dict_category.proto

package dict_category

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 字典分类基本信息
type DictCategory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,10,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	TenantId      int64                  `protobuf:"varint,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Sort          float32                `protobuf:"fixed32,4,opt,name=sort,proto3" json:"sort,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	CreatedBy     int64                  `protobuf:"varint,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	UpdatedBy     int64                  `protobuf:"varint,7,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,8,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,9,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictCategory) Reset() {
	*x = DictCategory{}
	mi := &file_dict_category_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictCategory) ProtoMessage() {}

func (x *DictCategory) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictCategory.ProtoReflect.Descriptor instead.
func (*DictCategory) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{0}
}

func (x *DictCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DictCategory) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *DictCategory) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *DictCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictCategory) GetSort() float32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *DictCategory) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DictCategory) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *DictCategory) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *DictCategory) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *DictCategory) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 创建字典分类请求
type CreateDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        int64                  `protobuf:"varint,7,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	TenantId      int64                  `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Sort          float32                `protobuf:"fixed32,3,opt,name=sort,proto3" json:"sort,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	CreatedBy     int64                  `protobuf:"varint,5,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,6,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictCategoryReq) Reset() {
	*x = CreateDictCategoryReq{}
	mi := &file_dict_category_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictCategoryReq) ProtoMessage() {}

func (x *CreateDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictCategoryReq.ProtoReflect.Descriptor instead.
func (*CreateDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDictCategoryReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *CreateDictCategoryReq) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *CreateDictCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictCategoryReq) GetSort() float32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateDictCategoryReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateDictCategoryReq) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *CreateDictCategoryReq) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

// 创建字典分类响应
type CreateDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictCategoryResp) Reset() {
	*x = CreateDictCategoryResp{}
	mi := &file_dict_category_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictCategoryResp) ProtoMessage() {}

func (x *CreateDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictCategoryResp.ProtoReflect.Descriptor instead.
func (*CreateDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDictCategoryResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 更新字典分类请求
type UpdateDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,8,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	TenantId      int64                  `protobuf:"varint,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Sort          float32                `protobuf:"fixed32,4,opt,name=sort,proto3" json:"sort,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	UpdatedBy     int64                  `protobuf:"varint,6,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,7,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictCategoryReq) Reset() {
	*x = UpdateDictCategoryReq{}
	mi := &file_dict_category_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictCategoryReq) ProtoMessage() {}

func (x *UpdateDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictCategoryReq.ProtoReflect.Descriptor instead.
func (*UpdateDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDictCategoryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictCategoryReq) GetSort() float32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 更新字典分类响应
type UpdateDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictCategoryResp) Reset() {
	*x = UpdateDictCategoryResp{}
	mi := &file_dict_category_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictCategoryResp) ProtoMessage() {}

func (x *UpdateDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictCategoryResp.ProtoReflect.Descriptor instead.
func (*UpdateDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDictCategoryResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 删除字典分类请求
type DeleteDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     int64                  `protobuf:"varint,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictCategoryReq) Reset() {
	*x = DeleteDictCategoryReq{}
	mi := &file_dict_category_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictCategoryReq) ProtoMessage() {}

func (x *DeleteDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictCategoryReq.ProtoReflect.Descriptor instead.
func (*DeleteDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDictCategoryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteDictCategoryReq) GetDeletedBy() int64 {
	if x != nil {
		return x.DeletedBy
	}
	return 0
}

// 删除字典分类响应
type DeleteDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictCategoryResp) Reset() {
	*x = DeleteDictCategoryResp{}
	mi := &file_dict_category_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictCategoryResp) ProtoMessage() {}

func (x *DeleteDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictCategoryResp.ProtoReflect.Descriptor instead.
func (*DeleteDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteDictCategoryResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 获取字典分类详情请求
type GetDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictCategoryReq) Reset() {
	*x = GetDictCategoryReq{}
	mi := &file_dict_category_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictCategoryReq) ProtoMessage() {}

func (x *GetDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictCategoryReq.ProtoReflect.Descriptor instead.
func (*GetDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{7}
}

func (x *GetDictCategoryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典分类详情响应
type GetDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictCategory  *DictCategory          `protobuf:"bytes,1,opt,name=dict_category,json=dictCategory,proto3" json:"dict_category,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictCategoryResp) Reset() {
	*x = GetDictCategoryResp{}
	mi := &file_dict_category_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictCategoryResp) ProtoMessage() {}

func (x *GetDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictCategoryResp.ProtoReflect.Descriptor instead.
func (*GetDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{8}
}

func (x *GetDictCategoryResp) GetDictCategory() *DictCategory {
	if x != nil {
		return x.DictCategory
	}
	return nil
}

// 字典分类列表请求
type ListDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictCategoryReq) Reset() {
	*x = ListDictCategoryReq{}
	mi := &file_dict_category_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictCategoryReq) ProtoMessage() {}

func (x *ListDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictCategoryReq.ProtoReflect.Descriptor instead.
func (*ListDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{9}
}

func (x *ListDictCategoryReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictCategoryReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictCategoryReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 字典分类列表响应
type ListDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*DictCategory        `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictCategoryResp) Reset() {
	*x = ListDictCategoryResp{}
	mi := &file_dict_category_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictCategoryResp) ProtoMessage() {}

func (x *ListDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_category_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictCategoryResp.ProtoReflect.Descriptor instead.
func (*ListDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_category_proto_rawDescGZIP(), []int{10}
}

func (x *ListDictCategoryResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictCategoryResp) GetList() []*DictCategory {
	if x != nil {
		return x.List
	}
	return nil
}

var File_dict_category_proto protoreflect.FileDescriptor

const file_dict_category_proto_rawDesc = "" +
	"\n" +
	"\x13dict_category.proto\x12\rdict_category\"\x98\x02\n" +
	"\fDictCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\n" +
	" \x01(\x03R\x06dictId\x12\x1b\n" +
	"\ttenant_id\x18\x02 \x01(\x03R\btenantId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x02R\x04sort\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_by\x18\x06 \x01(\x03R\tcreatedBy\x12\x1d\n" +
	"\n" +
	"updated_by\x18\a \x01(\x03R\tupdatedBy\x12!\n" +
	"\fcreated_time\x18\b \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\t \x01(\tR\vupdatedTime\"\xcf\x01\n" +
	"\x15CreateDictCategoryReq\x12\x17\n" +
	"\adict_id\x18\a \x01(\x03R\x06dictId\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x02R\x04sort\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_by\x18\x05 \x01(\x03R\tcreatedBy\x12!\n" +
	"\fcreated_time\x18\x06 \x01(\tR\vcreatedTime\"(\n" +
	"\x16CreateDictCategoryResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xdf\x01\n" +
	"\x15UpdateDictCategoryReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\b \x01(\x03R\x06dictId\x12\x1b\n" +
	"\ttenant_id\x18\x02 \x01(\x03R\btenantId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x02R\x04sort\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x06 \x01(\x03R\tupdatedBy\x12!\n" +
	"\fupdated_time\x18\a \x01(\tR\vupdatedTime\"2\n" +
	"\x16UpdateDictCategoryResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"F\n" +
	"\x15DeleteDictCategoryReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\x03R\tdeletedBy\"2\n" +
	"\x16DeleteDictCategoryResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"$\n" +
	"\x12GetDictCategoryReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"W\n" +
	"\x13GetDictCategoryResp\x12@\n" +
	"\rdict_category\x18\x01 \x01(\v2\x1b.dict_category.DictCategoryR\fdictCategory\"r\n" +
	"\x13ListDictCategoryReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\"]\n" +
	"\x14ListDictCategoryResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12/\n" +
	"\x04list\x18\x02 \x03(\v2\x1b.dict_category.DictCategoryR\x04list2\xf5\x03\n" +
	"\x13DictCategoryService\x12a\n" +
	"\x12CreateDictCategory\x12$.dict_category.CreateDictCategoryReq\x1a%.dict_category.CreateDictCategoryResp\x12a\n" +
	"\x12UpdateDictCategory\x12$.dict_category.UpdateDictCategoryReq\x1a%.dict_category.UpdateDictCategoryResp\x12a\n" +
	"\x12DeleteDictCategory\x12$.dict_category.DeleteDictCategoryReq\x1a%.dict_category.DeleteDictCategoryResp\x12X\n" +
	"\x0fGetDictCategory\x12!.dict_category.GetDictCategoryReq\x1a\".dict_category.GetDictCategoryResp\x12[\n" +
	"\x10ListDictCategory\x12\".dict_category.ListDictCategoryReq\x1a#.dict_category.ListDictCategoryRespB\x11Z\x0f./dict_categoryb\x06proto3"

var (
	file_dict_category_proto_rawDescOnce sync.Once
	file_dict_category_proto_rawDescData []byte
)

func file_dict_category_proto_rawDescGZIP() []byte {
	file_dict_category_proto_rawDescOnce.Do(func() {
		file_dict_category_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dict_category_proto_rawDesc), len(file_dict_category_proto_rawDesc)))
	})
	return file_dict_category_proto_rawDescData
}

var file_dict_category_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_dict_category_proto_goTypes = []any{
	(*DictCategory)(nil),           // 0: dict_category.DictCategory
	(*CreateDictCategoryReq)(nil),  // 1: dict_category.CreateDictCategoryReq
	(*CreateDictCategoryResp)(nil), // 2: dict_category.CreateDictCategoryResp
	(*UpdateDictCategoryReq)(nil),  // 3: dict_category.UpdateDictCategoryReq
	(*UpdateDictCategoryResp)(nil), // 4: dict_category.UpdateDictCategoryResp
	(*DeleteDictCategoryReq)(nil),  // 5: dict_category.DeleteDictCategoryReq
	(*DeleteDictCategoryResp)(nil), // 6: dict_category.DeleteDictCategoryResp
	(*GetDictCategoryReq)(nil),     // 7: dict_category.GetDictCategoryReq
	(*GetDictCategoryResp)(nil),    // 8: dict_category.GetDictCategoryResp
	(*ListDictCategoryReq)(nil),    // 9: dict_category.ListDictCategoryReq
	(*ListDictCategoryResp)(nil),   // 10: dict_category.ListDictCategoryResp
}
var file_dict_category_proto_depIdxs = []int32{
	0,  // 0: dict_category.GetDictCategoryResp.dict_category:type_name -> dict_category.DictCategory
	0,  // 1: dict_category.ListDictCategoryResp.list:type_name -> dict_category.DictCategory
	1,  // 2: dict_category.DictCategoryService.CreateDictCategory:input_type -> dict_category.CreateDictCategoryReq
	3,  // 3: dict_category.DictCategoryService.UpdateDictCategory:input_type -> dict_category.UpdateDictCategoryReq
	5,  // 4: dict_category.DictCategoryService.DeleteDictCategory:input_type -> dict_category.DeleteDictCategoryReq
	7,  // 5: dict_category.DictCategoryService.GetDictCategory:input_type -> dict_category.GetDictCategoryReq
	9,  // 6: dict_category.DictCategoryService.ListDictCategory:input_type -> dict_category.ListDictCategoryReq
	2,  // 7: dict_category.DictCategoryService.CreateDictCategory:output_type -> dict_category.CreateDictCategoryResp
	4,  // 8: dict_category.DictCategoryService.UpdateDictCategory:output_type -> dict_category.UpdateDictCategoryResp
	6,  // 9: dict_category.DictCategoryService.DeleteDictCategory:output_type -> dict_category.DeleteDictCategoryResp
	8,  // 10: dict_category.DictCategoryService.GetDictCategory:output_type -> dict_category.GetDictCategoryResp
	10, // 11: dict_category.DictCategoryService.ListDictCategory:output_type -> dict_category.ListDictCategoryResp
	7,  // [7:12] is the sub-list for method output_type
	2,  // [2:7] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_dict_category_proto_init() }
func file_dict_category_proto_init() {
	if File_dict_category_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dict_category_proto_rawDesc), len(file_dict_category_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dict_category_proto_goTypes,
		DependencyIndexes: file_dict_category_proto_depIdxs,
		MessageInfos:      file_dict_category_proto_msgTypes,
	}.Build()
	File_dict_category_proto = out.File
	file_dict_category_proto_goTypes = nil
	file_dict_category_proto_depIdxs = nil
}
