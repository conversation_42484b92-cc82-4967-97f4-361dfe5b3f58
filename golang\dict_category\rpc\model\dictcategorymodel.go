package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ DictCategoryModel = (*customDictCategoryModel)(nil)

type (
	// DictCategoryModel is an interface to be customized, add more methods here,
	// and implement the added methods in customDictCategoryModel.
	DictCategoryModel interface {
		dictCategoryModel
		LogicDelete(ctx context.Context, id, deletedBy int64) error
		FindListAndTotal(ctx context.Context, page, pageSize int, name string, status int) ([]*DictCategory, int64, error)
	}

	customDictCategoryModel struct {
		*defaultDictCategoryModel
	}
)

// NewDictCategoryModel returns a model for the database table.
func NewDictCategoryModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) DictCategoryModel {
	return &customDictCategoryModel{
		defaultDictCategoryModel: newDictCategoryModel(conn, c, opts...),
	}
}

// LogicDelete 逻辑删除
func (m *customDictCategoryModel) LogicDelete(ctx context.Context, id, deletedBy int64) error {
	dictCategoryIdKey := fmt.Sprintf("%s%v", cacheDictCategoryIdPrefix, id)

	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set deleted_time = now(), updated_by = ?, status = 0 where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, deletedBy, id)
	}, dictCategoryIdKey)

	return err
}

// FindListAndTotal 查询列表和总数
func (m *customDictCategoryModel) FindListAndTotal(ctx context.Context, page, pageSize int, name string, status int) ([]*DictCategory, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where deleted_time is null", dictCategoryRows, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where deleted_time is null", m.table)

	var args []interface{}

	if name != "" {
		query += " and name like ?"
		countQuery += " and name like ?"
		args = append(args, "%"+name+"%")
	}

	if status > 0 {
		query += " and status = ?"
		countQuery += " and status = ?"
		args = append(args, status)
	}

	query += " order by sort desc limit ? offset ?"
	args = append(args, pageSize, offset)

	var list []*DictCategory
	err := m.QueryRowsNoCacheCtx(ctx, &list, query, args...)
	if err != nil {
		return nil, 0, err
	}

	var total int64
	err = m.QueryRowNoCacheCtx(ctx, &total, countQuery, args[:len(args)-2]...)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
