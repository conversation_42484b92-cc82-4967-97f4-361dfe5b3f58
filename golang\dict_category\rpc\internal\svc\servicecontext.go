package svc

import (
	"golang-docker-compose-main/golang/dict_category/rpc/internal/config"
	"golang-docker-compose-main/golang/dict_category/rpc/model"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type ServiceContext struct {
	Config            config.Config
	DictCategoryModel model.DictCategoryModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	conn := sqlx.NewMysql(c.MySql.DataSource)
	return &ServiceContext{
		Config:            c,
		DictCategoryModel: model.NewDictCategoryModel(conn, c.CacheRedis),
	}
}
