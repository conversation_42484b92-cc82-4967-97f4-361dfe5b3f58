{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "swagger": "2.0", "info": {"title": "字典服务API", "version": "v1"}, "basePath": "/", "paths": {"/api/dict/create": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "createDict", "operationId": "createDict", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["code", "name", "showType"], "properties": {"categoryId": {"type": "integer"}, "code": {"type": "string"}, "name": {"type": "string"}, "remark": {"type": "string"}, "showType": {"type": "string"}, "status": {"type": "integer"}, "tenantId": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"base": {"type": "object", "required": ["code", "msg"], "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}, "id": {"type": "integer"}}}}}}}, "/api/dict/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "deleteDict", "operationId": "deleteDict", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["id", "deletedBy"], "properties": {"deletedBy": {"type": "integer"}, "id": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"base": {"type": "object", "required": ["code", "msg"], "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}, "success": {"type": "boolean"}}}}}}}, "/api/dict/get": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "getDict", "operationId": "getDict", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"base": {"type": "object", "required": ["code", "msg"], "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}, "dict": {"type": "object", "required": ["id", "code", "name", "showType"], "properties": {"categoryId": {"type": "integer"}, "code": {"type": "string"}, "createdBy": {"type": "integer"}, "createdTime": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "remark": {"type": "string"}, "showType": {"type": "string"}, "status": {"type": "integer"}, "tenantId": {"type": "integer"}, "updatedTime": {"type": "string"}}}}}}}}}, "/api/dict/list": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "listDict", "operationId": "listDict", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"base": {"type": "object", "required": ["code", "msg"], "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}, "list": {"type": "array", "items": {"type": "object", "required": ["id", "code", "name", "showType"], "properties": {"categoryId": {"type": "integer"}, "code": {"type": "string"}, "createdBy": {"type": "integer"}, "createdTime": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "remark": {"type": "string"}, "showType": {"type": "string"}, "status": {"type": "integer"}, "tenantId": {"type": "integer"}, "updatedTime": {"type": "string"}}}}, "total": {"type": "integer"}}}}}}}, "/api/dict/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "updateDict", "operationId": "updateDict", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["id"], "properties": {"categoryId": {"type": "integer"}, "code": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "remark": {"type": "string"}, "showType": {"type": "string"}, "status": {"type": "integer"}, "tenantId": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"base": {"type": "object", "required": ["code", "msg"], "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}, "success": {"type": "boolean"}}}}}}}}, "x-date": "2025-07-22 09:37:59", "x-description": "This is a goctl generated swagger file.", "x-github": "https://github.com/zeromicro/go-zero", "x-go-zero-doc": "https://go-zero.dev/", "x-goctl-version": "1.8.5"}