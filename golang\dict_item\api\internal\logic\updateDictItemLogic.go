package logic

import (
	"context"

	"dict_item/api/internal/svc"
	"dict_item/api/internal/types"
	"dict_item/rpc/dictitemclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新字典项
func NewUpdateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictItemLogic {
	return &UpdateDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictItemLogic) UpdateDictItem(req *types.UpdateDictItemReq) (resp *types.UpdateDictItemResp, err error) {
	// 调用RPC服务更新字典项
	_, err = l.svcCtx.DictItemRpc.UpdateDictItem(l.ctx, &dictitemclient.UpdateDictItemReq{
		Id:         req.Id,
		TenantId:   req.TenantId,
		DictId:     req.DictId,
		CategoryId: req.CategoryId,
		Code:       req.Code,
		Name:       req.Name,
		Pid:        req.Pid,
		Sort:       req.Sort,
		Status:     int32(req.Status),
		UpdatedBy:  req.UpdatedBy, // 直接使用用户传递的值
	})

	if err != nil {
		return &types.UpdateDictItemResp{
			Base: types.Base{
				Code: 500,
				Msg:  err.Error(),
			},
		}, nil
	}

	return &types.UpdateDictItemResp{
		Base: types.Base{
			Code: 200,
			Msg:  "success",
		},
	}, nil
}
