syntax = "v1"

info (
	title:   "字典项管理API"
	desc:    "字典项管理相关接口"
	author:  "API开发团队"
	email:   "<EMAIL>"
	version: "v1.0"
)

type (
	// 基础响应
	Base {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	// 字典项基础信息
	DictItem {
		Id          int64   `json:"id"`
		TenantId    int64   `json:"tenantId"`
		DictId      int64   `json:"dictId"`
		CategoryId  int64   `json:"categoryId"`
		Code        string  `json:"code"`
		Name        string  `json:"name"`
		Pid         int64   `json:"pid"`
		Sort        float64 `json:"sort"`
		Status      int     `json:"status"`
		CreatedBy   int64   `json:"createdBy"`
		CreatedTime string  `json:"createdTime"`
		UpdatedTime string  `json:"updatedTime"`
	}
	// 创建字典项请求
	CreateDictItemReq {
		TenantId   int64   `json:"tenantId,optional"`
		DictId     int64   `json:"dictId"`
		CategoryId int64   `json:"categoryId"`
		Code       string  `json:"code"`
		Name       string  `json:"name"`
		Pid        int64   `json:"pid,optional"`
		Sort       float64 `json:"sort,optional"`
		Status     int     `json:"status,optional"`
		CreatedBy  int64   `json:"createdBy"` // 添加人ID
	}
	// 创建字典项响应
	CreateDictItemResp {
		Base
		Id int64 `json:"id"`
	}
	// 更新字典项请求
	UpdateDictItemReq {
		Id         int64   `json:"id"`
		TenantId   int64   `json:"tenantId,optional"`
		DictId     int64   `json:"dictId,optional"`
		CategoryId int64   `json:"categoryId,optional"`
		Code       string  `json:"code,optional"`
		Name       string  `json:"name,optional"`
		Pid        int64   `json:"pid,optional"`
		Sort       float64 `json:"sort,optional"`
		Status     int     `json:"status,optional"`
		UpdatedBy  int64   `json:"updatedBy"` // 更新人ID
	}
	// 更新字典项响应
	UpdateDictItemResp {
		Base
	}
	// 删除字典项请求
	DeleteDictItemReq {
		Id        int64 `json:"id"`
		DeletedBy int64 `json:"deletedBy"` // 删除人ID
	}
	// 删除字典项响应
	DeleteDictItemResp {
		Base
	}
	// 获取字典项详情请求
	GetDictItemReq {
		Id int64 `json:"id"`
	}
	// 获取字典项详情响应
	GetDictItemResp {
		Base
		Data DictItem `json:"data"`
	}
	// 字典项列表请求
	ListDictItemReq {
		Page       int    `json:"page,optional"`
		PageSize   int    `json:"pageSize,optional"`
		DictId     int64  `json:"dictId,optional"`
		CategoryId int64  `json:"categoryId,optional"`
		Code       string `json:"code,optional"`
		Name       string `json:"name,optional"`
		Status     int    `json:"status,optional"`
	}
	// 字典项列表响应
	ListDictItemResp {
		Base
		Total int64      `json:"total"`
		List  []DictItem `json:"list"`
	}
)

service DictItem {
	@doc "创建字典项"
	@handler CreateDictItem
	post /api/dictitem/create (CreateDictItemReq) returns (CreateDictItemResp)

	@doc "更新字典项"
	@handler UpdateDictItem
	post /api/dictitem/update (UpdateDictItemReq) returns (UpdateDictItemResp)

	@doc "删除字典项"
	@handler DeleteDictItem
	post /api/dictitem/delete (DeleteDictItemReq) returns (DeleteDictItemResp)

	@doc "获取字典项详情"
	@handler GetDictItem
	post /api/dictitem/get (GetDictItemReq) returns (GetDictItemResp)

	@doc "获取字典项列表"
	@handler ListDictItem
	post /api/dictitem/list (ListDictItemReq) returns (ListDictItemResp)
}

