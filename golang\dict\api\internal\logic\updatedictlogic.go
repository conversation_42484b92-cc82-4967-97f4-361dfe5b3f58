package logic

import (
	"context"
	"dict/api/internal/svc"
	"dict/api/internal/types"
	"dict/api/middleware/JWT"
	"dict/rpc/dictservice"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type UpdateDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictLogic {
	return &UpdateDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictLogic) UpdateDict(req *types.UpdateDictReq) (resp *types.UpdateDictResp, err error) {
	// todo: add your logic here and delete this line

	// 调用RPC服务更新字典

	rpcResp, err := l.svcCtx.DictRpc.UpdateDict(l.ctx, &dictservice.UpdateDictReq{
		Id:          req.Id,
		TenantId:    req.TenantId,
		CategoryId:  req.CategoryId,
		Code:        req.Code,
		Name:        req.Name,
		ShowType:    req.ShowType,
		Remark:      req.Remark,
		Status:      int32(req.Status),
		UpdatedBy:   req.TenantId,
		UpdatedTime: time.Now().Format("2006-01-02 15:04:05"),
	})

	if err != nil {
		return nil, fmt.Errorf("更新字典失败：%s", err)
	}

	// 从配置文件中获取secret 、expire
	secret := l.svcCtx.Config.Auth.AccessSecret
	expire := l.svcCtx.Config.Auth.AccessExpire
	// 生成token
	token, err := JWT.GetJwtToken(secret, time.Now().Unix(), expire, int(req.TenantId))

	if err != nil {
		logx.Error("token生成失败:", err)
		return nil, err
	}
	return &types.UpdateDictResp{
		Success: rpcResp.Success,
		Base: types.Base{
			Code:  200,
			Msg:   "更新字典成功",
			Token: token,
		},
	}, nil
}
