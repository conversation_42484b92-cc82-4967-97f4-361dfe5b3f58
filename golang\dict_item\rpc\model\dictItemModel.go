package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ DictItemModel = (*customDictItemModel)(nil)

type (
	// DictItemModel is an interface to be customized, add more methods here,
	// and implement the added methods in customDictItemModel.
	DictItemModel interface {
		dictItemModel
		LogicDelete(ctx context.Context, id, deletedBy int64) error
		FindList(ctx context.Context, page, pageSize int, dictId, categoryId int64, code, name string, status int) ([]*DictItem, int64, error)
	}

	customDictItemModel struct {
		*defaultDictItemModel
	}
)

// NewDictItemModel returns a model for the database table.
func NewDictItemModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) DictItemModel {
	return &customDictItemModel{
		defaultDictItemModel: newDictItemModel(conn, c, opts...),
	}
}

// LogicDelete 逻辑删除
func (m *customDictItemModel) LogicDelete(ctx context.Context, id, deletedBy int64) error {
	dictItemIdKey := fmt.Sprintf("%s%v", cacheDictItemIdPrefix, id)

	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted_by` = ?, `deleted_time` = now(), `status` = 0 where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, deletedBy, id)
	}, dictItemIdKey)

	return err
}

// FindList 查询列表
func (m *customDictItemModel) FindList(ctx context.Context, page, pageSize int, dictId, categoryId int64, code, name string, status int) ([]*DictItem, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where `deleted_time` is null", dictItemRows, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where `deleted_time` is null", m.table)

	var args []interface{}
	var countArgs []interface{}

	if dictId > 0 {
		query += " and `dict_id` = ?"
		countQuery += " and `dict_id` = ?"
		args = append(args, dictId)
		countArgs = append(countArgs, dictId)
	}

	if categoryId > 0 {
		query += " and `category_id` = ?"
		countQuery += " and `category_id` = ?"
		args = append(args, categoryId)
		countArgs = append(countArgs, categoryId)
	}

	if code != "" {
		query += " and `code` like ?"
		countQuery += " and `code` like ?"
		args = append(args, "%"+code+"%")
		countArgs = append(countArgs, "%"+code+"%")
	}

	if name != "" {
		query += " and `name` like ?"
		countQuery += " and `name` like ?"
		args = append(args, "%"+name+"%")
		countArgs = append(countArgs, "%"+name+"%")
	}

	if status > 0 {
		query += " and `status` = ?"
		countQuery += " and `status` = ?"
		args = append(args, status)
		countArgs = append(countArgs, status)
	}

	query += " order by `sort` asc limit ? offset ?"
	args = append(args, pageSize, offset)

	var list []*DictItem
	err := m.QueryRowsNoCacheCtx(ctx, &list, query, args...)
	if err != nil {
		return nil, 0, err
	}

	var total int64
	err = m.QueryRowNoCacheCtx(ctx, &total, countQuery, countArgs...)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
