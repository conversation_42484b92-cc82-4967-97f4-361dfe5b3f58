package logic

import (
	"context"

	"dict_item/api/internal/svc"
	"dict_item/api/internal/types"
	"dict_item/rpc/dictitemclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取字典项详情
func NewGetDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictItemLogic {
	return &GetDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictItemLogic) GetDictItem(req *types.GetDictItemReq) (resp *types.GetDictItemResp, err error) {
	// 调用RPC服务获取字典项详情
	res, err := l.svcCtx.DictItemRpc.GetDictItem(l.ctx, &dictitemclient.GetDictItemReq{
		Id: req.Id,
	})

	if err != nil {
		return &types.GetDictItemResp{
			Base: types.Base{
				Code: 500,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 构建响应数据
	item := types.DictItem{
		Id:          res.Item.Id,
		TenantId:    res.Item.TenantId,
		DictId:      res.Item.DictId,
		CategoryId:  res.Item.CategoryId,
		Code:        res.Item.Code,
		Name:        res.Item.Name,
		Pid:         res.Item.Pid,
		Sort:        res.Item.Sort,
		Status:      int(res.Item.Status),
		CreatedBy:   res.Item.CreatedBy,
		CreatedTime: res.Item.CreatedTime,
		UpdatedTime: res.Item.UpdatedTime,
	}

	return &types.GetDictItemResp{
		Base: types.Base{
			Code: 200,
			Msg:  "success",
		},
		Data: item,
	}, nil
}
