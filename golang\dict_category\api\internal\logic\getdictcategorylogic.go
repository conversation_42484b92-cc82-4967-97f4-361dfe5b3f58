package logic

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"
	"golang-docker-compose-main/golang/dict_category/api/internal/types"
	"golang-docker-compose-main/golang/dict_category/rpc/dictcategoryservice"
)

type GetDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictCategoryLogic) GetDictCategory(req *types.GetDictCategoryReq) (resp *types.GetDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.DictCategoryRpc.GetDictCategory(l.ctx, &dictcategoryservice.GetDictCategoryReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC的GetDictCategory失败：%v", err)
	}

	return &types.GetDictCategoryResp{
		DictCategory: types.DictCategory{
			Id:          rpcResp.DictCategory.Id,
			TenantId:    rpcResp.DictCategory.TenantId,
			Name:        rpcResp.DictCategory.Name,
			Sort:        float64(rpcResp.DictCategory.Sort),
			Status:      int(rpcResp.DictCategory.Status),
			CreatedBy:   rpcResp.DictCategory.CreatedBy,
			UpdatedBy:   rpcResp.DictCategory.UpdatedBy,
			CreatedTime: rpcResp.DictCategory.CreatedTime,
			UpdatedTime: rpcResp.DictCategory.UpdatedTime,
		},
		Base: types.Base{
			Code: 200,
			Msg:  "调用RPC的GetDictCategory获取数据成功",
		},
	}, nil
}
