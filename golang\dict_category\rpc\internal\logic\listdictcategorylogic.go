package logic

import (
	"context"

	"golang-docker-compose-main/golang/dict_category/rpc/dict_category"
	"golang-docker-compose-main/golang/dict_category/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictCategoryLogic {
	return &ListDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典分类列表
func (l *ListDictCategoryLogic) ListDictCategory(in *dict_category.ListDictCategoryReq) (*dict_category.ListDictCategoryResp, error) {
	// 查询数据库
	categories, total, err := l.svcCtx.DictCategoryModel.FindListAndTotal(
		l.ctx,
		int(in.Page),
		int(in.PageSize),
		in.Name,
		int(in.Status),
	)
	if err != nil {
		return nil, err
	}

	// 构造返回结果
	list := make([]*dict_category.DictCategory, 0, len(categories))
	for _, category := range categories {
		item := &dict_category.DictCategory{
			Id:       category.Id,
			TenantId: category.TenantId,
			Name:     category.Name,
			Sort:     float32(category.Sort),
			Status:   int32(category.Status),
		}

		// 处理可空字段
		if category.CreatedBy.Valid {
			item.CreatedBy = category.CreatedBy.Int64
		}
		if category.UpdatedBy.Valid {
			item.UpdatedBy = category.UpdatedBy.Int64
		}
		if category.CreatedTime.Valid {
			item.CreatedTime = category.CreatedTime.Time.Format("2006-01-02 15:04:05")
		}
		if category.UpdatedTime.Valid {
			item.UpdatedTime = category.UpdatedTime.Time.Format("2006-01-02 15:04:05")
		}

		list = append(list, item)
	}

	return &dict_category.ListDictCategoryResp{
		Total: total,
		List:  list,
	}, nil
}
