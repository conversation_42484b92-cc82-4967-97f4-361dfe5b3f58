package logic

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"
	"golang-docker-compose-main/golang/dict_category/api/internal/types"
	"golang-docker-compose-main/golang/dict_category/rpc/dictcategoryservice"
	"time"
)

type UpdateDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictCategoryLogic) UpdateDictCategory(req *types.UpdateDictCategoryReq) (resp *types.UpdateDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.DictCategoryRpc.UpdateDictCategory(l.ctx, &dictcategoryservice.UpdateDictCategoryReq{
		Id:          req.Id,
		TenantId:    req.TenantId,
		Name:        req.Name,
		Sort:        float32(req.Sort),
		Status:      int32(req.Status),
		UpdatedBy:   req.UpdatedBy,
		UpdatedTime: time.Now().Format("2006-01-02 15:04:05"),
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC的UpdateDictCategory失败: %v", err)
	}

	return &types.UpdateDictCategoryResp{
		Success: rpcResp.Success,
		Base: types.Base{
			Code: 200,
			Msg:  "调用RPC的UpdateDictCategory更新成功",
		},
	}, nil
}
