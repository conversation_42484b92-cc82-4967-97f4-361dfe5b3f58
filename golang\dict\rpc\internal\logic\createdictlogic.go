package logic

import (
	"context"
	"dict/rpc/dict"
	"dict/rpc/internal/svc"
	"fmt"
	"mp/rpc/model"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典
func (l *CreateDictLogic) CreateDict(in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	// 添加日志
	l.Infof("接收到创建字典请求: %+v", in)

	// 检查DB是否为nil
	if l.svcCtx.DB == nil {
		return nil, fmt.Errorf("数据库连接为空，请检查数据库配置")
	}

	// 如果没有提供 created_time，可以在这里设置默认值
	currentTime := time.Now()
	if in.CreatedTime == "" {
		in.CreatedTime = currentTime.Format("2006-01-02 15:04:05")
	}

	// 检查必要参数
	if in.Code == "" {
		return nil, fmt.Errorf("字典编码不能为空")
	}
	if in.Name == "" {
		return nil, fmt.Errorf("字典名称不能为空")
	}
	if in.ShowType == "" {
		return nil, fmt.Errorf("显示类型不能为空")
	}

	// 转换Code字符串为整数
	Code, err := strconv.Atoi(in.Code)
	if err != nil {
		l.Errorf("字典编码转换失败: %v", err)
		return nil, fmt.Errorf("字典编码必须是有效的数字: %v", err)
	}

	// 将数据插入数据库
	dictData := &model.Dicts{
		TenantId:   uint(in.TenantId),
		CategoryId: uint(in.CategoryId),
		Code:       Code,
		Name:       in.Name,
		ShowType:   in.ShowType,
		Remark:     in.Remark,
		CreatedAt:  currentTime,
		Status:     int(in.Status),
		CreatedBy:  uint(in.CreatedBy),
	}

	l.Infof("准备插入数据库的记录: %+v", dictData)

	// 使用gorm创建记录
	result := l.svcCtx.DB.Create(dictData)
	if result.Error != nil {
		l.Errorf("创建字典失败: %v", result.Error)
		return nil, fmt.Errorf("创建字典失败: %v", result.Error)
	}

	l.Infof("创建字典成功，ID: %d", dictData.ID)
	return &dict.CreateDictResp{
		Id: int64(dictData.ID),
	}, nil
}
