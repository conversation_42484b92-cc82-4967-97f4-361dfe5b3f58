// Code generated by goctl. DO NOT EDIT.
// Source: dict_item.proto

package dictitemclient

import (
	"context"

	"dict_item/rpc/dict_item"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateDictItemReq  = dict_item.CreateDictItemReq
	CreateDictItemResp = dict_item.CreateDictItemResp
	DeleteDictItemReq  = dict_item.DeleteDictItemReq
	DeleteDictItemResp = dict_item.DeleteDictItemResp
	DictItem           = dict_item.DictItem
	GetDictItemReq     = dict_item.GetDictItemReq
	GetDictItemResp    = dict_item.GetDictItemResp
	ListDictItemReq    = dict_item.ListDictItemReq
	ListDictItemResp   = dict_item.ListDictItemResp
	UpdateDictItemReq  = dict_item.UpdateDictItemReq
	UpdateDictItemResp = dict_item.UpdateDictItemResp

	DictItemService interface {
		// 创建字典项
		CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error)
		// 更新字典项
		UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error)
		// 删除字典项
		DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error)
		// 获取字典项详情
		GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error)
		// 获取字典项列表
		ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error)
	}

	defaultDictItemService struct {
		cli zrpc.Client
	}
)

func NewDictItemService(cli zrpc.Client) DictItemService {
	return &defaultDictItemService{
		cli: cli,
	}
}

// 创建字典项
func (m *defaultDictItemService) CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error) {
	client := dict_item.NewDictItemServiceClient(m.cli.Conn())
	return client.CreateDictItem(ctx, in, opts...)
}

// 更新字典项
func (m *defaultDictItemService) UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error) {
	client := dict_item.NewDictItemServiceClient(m.cli.Conn())
	return client.UpdateDictItem(ctx, in, opts...)
}

// 删除字典项
func (m *defaultDictItemService) DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error) {
	client := dict_item.NewDictItemServiceClient(m.cli.Conn())
	return client.DeleteDictItem(ctx, in, opts...)
}

// 获取字典项详情
func (m *defaultDictItemService) GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error) {
	client := dict_item.NewDictItemServiceClient(m.cli.Conn())
	return client.GetDictItem(ctx, in, opts...)
}

// 获取字典项列表
func (m *defaultDictItemService) ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error) {
	client := dict_item.NewDictItemServiceClient(m.cli.Conn())
	return client.ListDictItem(ctx, in, opts...)
}
