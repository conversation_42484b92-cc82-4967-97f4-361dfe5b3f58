package logic

import (
	"context"
	"dict/api/internal/svc"
	"dict/api/internal/types"
	"dict/api/middleware/JWT"
	"dict/rpc/dictservice"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictLogic) CreateDict(req *types.CreateDictReq) (resp *types.CreateDictResp, err error) {
	// 记录请求日志
	l.Infof("接收到创建字典请求: %+v", req)

	// 调用 RPC 服务

	rpcResp, err := l.svcCtx.DictRpc.CreateDict(l.ctx, &dictservice.CreateDictReq{
		TenantId:   req.TenantId,
		CategoryId: req.CategoryId,
		Name:       req.Name,
		Code:       req.Code,
		ShowType:   req.ShowType,
		Remark:     req.Remark,
		Status:     int32(req.Status),
		CreatedBy:  req.CreatedBy,
	})
	if err != nil {
		return nil, fmt.Errorf("创建字典失败：%v", err)
	}

	// 从配置文件中获取secret 、expire
	secret := l.svcCtx.Config.Auth.AccessSecret
	expire := l.svcCtx.Config.Auth.AccessExpire
	// 生成token
	token, err := JWT.GetJwtToken(secret, time.Now().Unix(), expire, int(req.TenantId))

	if err != nil {
		logx.Error("token生成失败:", err)
		return nil, err
	}
	return &types.CreateDictResp{
		Id: rpcResp.Id,
		Base: types.Base{
			Code:  200,
			Msg:   "创建字典成功",
			Token: token,
		},
	}, nil
}
