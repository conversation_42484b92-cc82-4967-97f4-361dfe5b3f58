﻿package logic

import (
	"context"
	"dict/rpc/dict"
	"dict/rpc/internal/svc"
	"dict/rpc/model"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictLogic {
	return &UpdateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典
func (l *UpdateDictLogic) UpdateDict(in *dict.UpdateDictReq) (*dict.UpdateDictResp, error) {
	// 先查询是否存在该记录
	var existingDict model.Dicts
	result := l.svcCtx.DB.First(&existingDict, in.Id)
	if result.Error != nil {
		return nil, fmt.Errorf("查询原始数据失败: %v", result.Error)
	}

	// 如果没有找到记录
	if result.RowsAffected == 0 {
		return nil, fmt.Errorf("未找到ID为%d的字典记录", in.Id)
	}

	// 转换Code字段
	Code, err := strconv.Atoi(in.Code)
	if err != nil {
		return nil, fmt.Errorf("编码格式错误: %v", err)
	}

	// 构建更新数据
	updateData := map[string]interface{}{
		"tenant_id":   uint(in.TenantId),
		"category_id": uint(in.CategoryId),
		"code":        Code,
		"name":        in.Name,
		"show_type":   in.ShowType,
		"remark":      in.Remark,
		"status":      int(in.Status),
		"updated_by":  uint(in.UpdatedBy),
		"updated_at":  time.Now(),
	}

	// 执行更新操作
	result = l.svcCtx.DB.Model(&existingDict).Updates(updateData)
	if result.Error != nil {
		return nil, fmt.Errorf("更新字典失败: %v", result.Error)
	}

	return &dict.UpdateDictResp{
		Success: true,
	}, nil
}
