// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type Base struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Token string `json:"token"`
}

type CreateDictReq struct {
	TenantId   int64  `json:"tenantId,optional"`
	CategoryId int64  `json:"categoryId,optional"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	ShowType   string `json:"showType"`
	Remark     string `json:"remark,optional"`
	Status     int    `json:"status,optional"`
	CreatedBy  int64  `json:"createdBy,optional"`
}

type CreateDictResp struct {
	Id   int64 `json:"id"`
	Base Base  `json:"base"`
}

type DeleteDictReq struct {
	Id        int64 `json:"id"`
	DeletedBy int64 `json:"deletedBy"`
}

type DeleteDictResp struct {
	Success bool `json:"success"`
	Base    Base `json:"base"`
}

type Dict struct {
	Id          int64  `json:"id"`
	TenantId    int64  `json:"tenantId,optional"`
	CategoryId  int64  `json:"categoryId,optional"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	ShowType    string `json:"showType"`
	Remark      string `json:"remark,optional"`
	Status      int    `json:"status,optional"`
	CreatedBy   int64  `json:"createdBy,optional"`
	CreatedTime string `json:"createdTime,optional"`
	UpdatedTime string `json:"updatedTime,optional"`
}

type GetDictReq struct {
	Id int64 `json:"id"`
}

type GetDictResp struct {
	Dict Dict `json:"dict"`
	Base Base `json:"base"`
}

type ListDictReq struct {
	Page     int `json:"page,optional"`
	PageSize int `json:"pageSize,optional"`
}

type ListDictResp struct {
	Total int64  `json:"total"`
	List  []Dict `json:"list"`
	Base  Base   `json:"base"`
}

type UpdateDictReq struct {
	Id         int64  `json:"id"`
	TenantId   int64  `json:"tenantId,optional"`
	CategoryId int64  `json:"categoryId,optional"`
	Code       string `json:"code,optional"`
	Name       string `json:"name,optional"`
	ShowType   string `json:"showType,optional"`
	Remark     string `json:"remark,optional"`
	Status     int    `json:"status,optional"`
}

type UpdateDictResp struct {
	Success bool `json:"success"`
	Base    Base `json:"base"`
}
