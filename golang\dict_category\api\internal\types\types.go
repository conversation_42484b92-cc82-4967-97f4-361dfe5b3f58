// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type Base struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type CreateDictCategoryReq struct {
	DictId    int64   `json:"dictId"`
	TenantId  int64   `json:"tenantId,optional"`
	Name      string  `json:"name"`
	Sort      float64 `json:"sort,optional"`
	Status    int     `json:"status,optional"`
	CreatedBy int64   `json:"createdBy,optional"`
}

type CreateDictCategoryResp struct {
	Id   int64 `json:"id"`
	Base Base  `json:"base"`
}

type DeleteDictCategoryReq struct {
	Id        int64 `json:"id"`
	DeletedBy int64 `json:"deletedBy"`
}

type DeleteDictCategoryResp struct {
	Success bool `json:"success"`
	Base    Base `json:"base"`
}

type DictCategory struct {
	Id          int64   `json:"id"`
	DictId      int64   `json:"dictId"`
	TenantId    int64   `json:"tenantId,optional"`
	Name        string  `json:"name"`
	Sort        float64 `json:"sort,optional"`
	Status      int     `json:"status,optional"`
	CreatedBy   int64   `json:"createdBy,optional"`
	UpdatedBy   int64   `json:"updatedBy,optional"`
	CreatedTime string  `json:"createdTime,optional"`
	UpdatedTime string  `json:"updatedTime,optional"`
}

type GetDictCategoryReq struct {
	Id int64 `json:"id"`
}

type GetDictCategoryResp struct {
	DictCategory DictCategory `json:"dictCategory"`
	Base         Base         `json:"base"`
}

type ListDictCategoryReq struct {
	Page     int    `json:"page,optional"`
	PageSize int    `json:"pageSize,optional"`
	Name     string `json:"name,optional"`
	Status   int    `json:"status,optional"`
}

type ListDictCategoryResp struct {
	Total int64          `json:"total"`
	List  []DictCategory `json:"list"`
	Base  Base           `json:"base"`
}

type UpdateDictCategoryReq struct {
	Id        int64   `json:"id"`
	DictId    int64   `json:"dictId,optional"`
	TenantId  int64   `json:"tenantId,optional"`
	Name      string  `json:"name,optional"`
	Sort      float64 `json:"sort,optional"`
	Status    int     `json:"status,optional"`
	UpdatedBy int64   `json:"updatedBy,optional"`
}

type UpdateDictCategoryResp struct {
	Success bool `json:"success"`
	Base    Base `json:"base"`
}
