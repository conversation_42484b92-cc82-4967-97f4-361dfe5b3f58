package svc

import (
	"dict_item/api/internal/config"
	"dict_item/rpc/dictitemclient"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config      config.Config
	DictItemRpc dictitemclient.DictItemService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:      c,
		DictItemRpc: dictitemclient.NewDictItemService(zrpc.MustNewClient(c.DictItemRpc)),
	}
}
