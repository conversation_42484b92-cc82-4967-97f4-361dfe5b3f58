syntax = "v1"

info (
	title:   "字典分类服务API"
	desc:    "字典分类表的增删改查API服务"
	author:  "goctl"
	email:   "<EMAIL>"
	version: "v1"
)

type (
	// 字典分类基本信息
	DictCategory {
		Id          int64   `json:"id"`
		DictId      int64   `json:"dictId"`
		TenantId    int64   `json:"tenantId,optional"`
		Name        string  `json:"name"`
		Sort        float64 `json:"sort,optional"`
		Status      int     `json:"status,optional"`
		CreatedBy   int64   `json:"createdBy,optional"`
		UpdatedBy   int64   `json:"updatedBy,optional"`
		CreatedTime string  `json:"createdTime,optional"`
		UpdatedTime string  `json:"updatedTime,optional"`
	}
	// 创建字典分类请求
	CreateDictCategoryReq {
		DictId    int64   `json:"dictId"`
		TenantId  int64   `json:"tenantId,optional"`
		Name      string  `json:"name"`
		Sort      float64 `json:"sort,optional"`
		Status    int     `json:"status,optional"`
		CreatedBy int64   `json:"createdBy,optional"`
	}
	Base {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	// 创建字典分类响应
	CreateDictCategoryResp {
		Id   int64 `json:"id"`
		Base Base  `json:"base"`
	}
	// 更新字典分类请求
	UpdateDictCategoryReq {
		Id        int64   `json:"id"`
		DictId    int64   `json:"dictId,optional"`
		TenantId  int64   `json:"tenantId,optional"`
		Name      string  `json:"name,optional"`
		Sort      float64 `json:"sort,optional"`
		Status    int     `json:"status,optional"`
		UpdatedBy int64   `json:"updatedBy,optional"`
	}
	// 更新字典分类响应
	UpdateDictCategoryResp {
		Success bool `json:"success"`
		Base    Base `json:"base"`
	}
	// 删除字典分类请求
	DeleteDictCategoryReq {
		Id        int64 `json:"id"`
		DeletedBy int64 `json:"deletedBy"`
	}
	// 删除字典分类响应
	DeleteDictCategoryResp {
		Success bool `json:"success"`
		Base    Base `json:"base"`
	}
	// 获取字典分类详情请求
	GetDictCategoryReq {
		Id int64 `json:"id"`
	}
	// 获取字典分类详情响应
	GetDictCategoryResp {
		DictCategory DictCategory `json:"dictCategory"`
		Base         Base         `json:"base"`
	}
	// 字典分类列表请求
	ListDictCategoryReq {
		Page     int    `json:"page,optional"`
		PageSize int    `json:"pageSize,optional"`
		Name     string `json:"name,optional"`
		Status   int    `json:"status,optional"`
	}
	// 字典分类列表响应
	ListDictCategoryResp {
		Total int64          `json:"total"`
		List  []DictCategory `json:"list"`
		Base  Base           `json:"base"`
	}
)

service dictcategoryapi {
	@handler createDictCategory
	post /api/dict/category/create (CreateDictCategoryReq) returns (CreateDictCategoryResp)

	@handler updateDictCategory
	post /api/dict/category/update (UpdateDictCategoryReq) returns (UpdateDictCategoryResp)

	@handler deleteDictCategory
	post /api/dict/category/delete (DeleteDictCategoryReq) returns (DeleteDictCategoryResp)

	@handler getDictCategory
	post /api/dict/category/get (GetDictCategoryReq) returns (GetDictCategoryResp)

	@handler listDictCategory
	post /api/dict/category/list (ListDictCategoryReq) returns (ListDictCategoryResp)
}

