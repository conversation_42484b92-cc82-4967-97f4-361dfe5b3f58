version: '3'

tasks:
  ps:
    dir: ./
    cmds:
      - docker ps -a

  run:
    dir: ./
    cmds:
      - docker-compose up -d

  build:
    dir: ./
    cmds:
      - docker-compose up -d --build dict-api dict-rpc

  cache:
    dir: ./
    cmds:
      - docker-compose build --no-cache dict-api dict-rpc

  docker_api:
    dir: ./api
    cmds:
      - goctl docker -go dict.go

  docker_rpc:
    dir: ./rpc
    cmds:
      - goctl docker -go dict.go

  api:
    dir: ./api
    cmds:
      - goctl api go -api dict.api -dir . -style gozero

  rpc:
    dir: ./rpc
    cmds:
      - goctl rpc protoc courseware.proto --go_out=. --go-grpc_out=. --zrpc_out=.