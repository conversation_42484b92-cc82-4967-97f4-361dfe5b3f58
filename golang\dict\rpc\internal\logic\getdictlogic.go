package logic

import (
	"context"
	"dict/rpc/dict"
	"dict/rpc/internal/svc"
	"fmt"
	"mp/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictLogic {
	return &GetDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典详情
func (l *GetDictLogic) GetDict(in *dict.GetDictReq) (*dict.GetDictResp, error) {
	// 使用gorm查询单个字典记录
	var dictData model.Dicts
	result := l.svcCtx.DB.First(&dictData, in.Id)
	if result.Error != nil {
		return nil, fmt.Errorf("获取字典详情失败: %v", result.Error)
	}

	// 如果没有找到记录
	if result.RowsAffected == 0 {
		return nil, fmt.Errorf("未找到ID为%d的字典记录", in.Id)
	}

	// 将查询结果转换为响应格式
	dictResp := &dict.Dict{
		Id:          int64(dictData.ID),
		TenantId:    int64(dictData.TenantId),
		CategoryId:  int64(dictData.CategoryId),
		Code:        fmt.Sprintf("%d", dictData.Code),
		Name:        dictData.Name,
		ShowType:    dictData.ShowType,
		Remark:      dictData.Remark,
		Status:      int32(dictData.Status),
		CreatedBy:   int64(dictData.CreatedBy),
		CreatedTime: dictData.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	// 如果有更新时间，也转换
	if !dictData.UpdatedAt.IsZero() {
		dictResp.UpdatedTime = dictData.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	return &dict.GetDictResp{
		Dict: dictResp,
	}, nil
}
