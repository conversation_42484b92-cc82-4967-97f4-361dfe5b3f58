// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	"dict_item/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 创建字典项
				Method:  http.MethodPost,
				Path:    "/api/dictitem/create",
				Handler: CreateDictItemHandler(serverCtx),
			},
			{
				// 删除字典项
				Method:  http.MethodPost,
				Path:    "/api/dictitem/delete",
				Handler: DeleteDictItemHandler(serverCtx),
			},
			{
				// 获取字典项详情
				Method:  http.MethodPost,
				Path:    "/api/dictitem/get",
				Handler: GetDictItemHandler(serverCtx),
			},
			{
				// 获取字典项列表
				Method:  http.MethodPost,
				Path:    "/api/dictitem/list",
				Handler: ListDictItemHandler(serverCtx),
			},
			{
				// 更新字典项
				Method:  http.MethodPost,
				Path:    "/api/dictitem/update",
				Handler: UpdateDictItemHandler(serverCtx),
			},
		},
	)
}
