package logic

import (
	"context"

	"dict_item/api/internal/svc"
	"dict_item/api/internal/types"
	"dict_item/rpc/dictitemclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取字典项列表
func NewListDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictItemLogic {
	return &ListDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictItemLogic) ListDictItem(req *types.ListDictItemReq) (resp *types.ListDictItemResp, err error) {
	// 调用RPC服务获取字典项列表
	res, err := l.svcCtx.DictItemRpc.ListDictItem(l.ctx, &dictitemclient.ListDictItemReq{
		Page:       int32(req.Page),
		PageSize:   int32(req.PageSize),
		DictId:     req.DictId,
		CategoryId: req.CategoryId,
		Code:       req.Code,
		Name:       req.Name,
		Status:     int32(req.Status),
	})

	if err != nil {
		return &types.ListDictItemResp{
			Base: types.Base{
				Code: 500,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 构建响应数据
	list := make([]types.DictItem, 0, len(res.List))
	for _, item := range res.List {
		list = append(list, types.DictItem{
			Id:          item.Id,
			TenantId:    item.TenantId,
			DictId:      item.DictId,
			CategoryId:  item.CategoryId,
			Code:        item.Code,
			Name:        item.Name,
			Pid:         item.Pid,
			Sort:        item.Sort,
			Status:      int(item.Status),
			CreatedBy:   item.CreatedBy,
			CreatedTime: item.CreatedTime,
			UpdatedTime: item.UpdatedTime,
		})
	}

	return &types.ListDictItemResp{
		Base: types.Base{
			Code: 200,
			Msg:  "success",
		},
		Total: res.Total,
		List:  list,
	}, nil
}
