version: '3'

tasks:
  ps:
    dir: ./
    cmds:
      - docker ps -a

  run:
    dir: ./
    cmds:
      - docker-compose up -d

  build:
    dir: ./
    cmds:
      - docker-compose up -d --build dict-api dict-rpc

  cache:
    dir: ./
    cmds:
      - docker-compose build --no-cache dict-api dict-rpc

  apidocker:
    dir: ./api
    cmds:
      - goctl docker -go dict.go

  rpcdocker:
    dir: ./rpc
    cmds:
      - goctl docker -go dict.go


  model:
    dir: ./rpc/model
    cmds:
      - goctl model mysql ddl -src="../create_dict_category.sql" -dir="./" -c

  proto:
    dir: ./rpc
    cmds:
      - goctl rpc protoc dict_category.proto --go_out=. --go-grpc_out=. --zrpc_out=.

  api:
    dir: ./api
    cmds:
      - goctl api go -api dict_category.api -dir . -style gozero