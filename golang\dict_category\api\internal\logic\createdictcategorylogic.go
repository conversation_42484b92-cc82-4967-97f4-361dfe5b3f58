package logic

import (
	"context"
	"fmt"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"
	"golang-docker-compose-main/golang/dict_category/api/internal/types"
	"golang-docker-compose-main/golang/dict_category/rpc/dictcategoryservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictCategoryLogic) CreateDictCategory(req *types.CreateDictCategoryReq) (resp *types.CreateDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	// 调用 RPC 服务
	rpcResp, err := l.svcCtx.DictCategoryRpc.CreateDictCategory(l.ctx, &dictcategoryservice.CreateDictCategoryReq{
		DictId:    req.DictId,
		TenantId:  req.TenantId,
		Name:      req.Name,
		Sort:      float32(req.Sort),
		Status:    int32(req.Status),
		CreatedBy: req.CreatedBy,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC的CreateDictCategory失败：%v", err)
	}

	return &types.CreateDictCategoryResp{
		Id: rpcResp.Id,
		Base: types.Base{
			Code: 200,
			Msg:  "调用RPC的CreateDictCategory创建成功",
		},
	}, nil
}
