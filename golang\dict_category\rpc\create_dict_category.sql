CREATE TABLE IF NOT EXISTS `dict_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) DEFAULT 0 COMMENT '租户ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '字段分类名称',
  `sort` float DEFAULT 0 COMMENT '排序值',
  `status` tinyint(2) DEFAULT 1 COMMENT '数据状态',
  `created_by` bigint(20) DEFAULT NULL COMMENT '操作人',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '编辑人',
  `created_time` datetime DEFAULT NULL COMMENT '添加时间',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted_time` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典分类表'; 