package logic

import (
	"context"

	"dict_item/rpc/dict_item"
	"dict_item/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictItemLogic {
	return &DeleteDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典项 (逻辑删除)
func (l *DeleteDictItemLogic) DeleteDictItem(in *dict_item.DeleteDictItemReq) (*dict_item.DeleteDictItemResp, error) {
	// 执行逻辑删除，设置deleted_by和deleted_time，并将status设为0
	err := l.svcCtx.DictItemModel.LogicDelete(l.ctx, in.Id, in.DeletedBy)
	if err != nil {
		return nil, err
	}

	return &dict_item.DeleteDictItemResp{
		Success: true,
	}, nil
}
