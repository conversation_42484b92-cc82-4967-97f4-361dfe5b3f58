package logic

import (
	"context"

	"dict_item/api/internal/svc"
	"dict_item/api/internal/types"
	"dict_item/rpc/dictitemclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建字典项
func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictItemLogic) CreateDictItem(req *types.CreateDictItemReq) (resp *types.CreateDictItemResp, err error) {
	// 调用RPC服务创建字典项
	res, err := l.svcCtx.DictItemRpc.CreateDictItem(l.ctx, &dictitemclient.CreateDictItemReq{
		TenantId:   req.TenantId,
		DictId:     req.DictId,
		CategoryId: req.CategoryId,
		Code:       req.Code,
		Name:       req.Name,
		Pid:        req.Pid,
		Sort:       req.Sort,
		Status:     int32(req.Status),
		CreatedBy:  req.CreatedBy, // 直接使用用户传递的值
	})

	if err != nil {
		return &types.CreateDictItemResp{
			Base: types.Base{
				Code: 500,
				Msg:  err.Error(),
			},
		}, nil
	}

	return &types.CreateDictItemResp{
		Base: types.Base{
			Code: 200,
			Msg:  "success",
		},
		Id: res.Id,
	}, nil
}
