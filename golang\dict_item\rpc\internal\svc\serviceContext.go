package svc

import (
	"dict_item/rpc/internal/config"
	"dict_item/rpc/model"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type ServiceContext struct {
	Config        config.Config
	DictItemModel model.DictItemModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	conn := sqlx.NewMysql(c.MySql.DataSource)
	return &ServiceContext{
		Config:        c,
		DictItemModel: model.NewDictItemModel(conn, c.CacheRedis),
	}
}
