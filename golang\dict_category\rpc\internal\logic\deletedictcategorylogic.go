package logic

import (
	"context"

	"golang-docker-compose-main/golang/dict_category/rpc/dict_category"
	"golang-docker-compose-main/golang/dict_category/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典分类 - 使用逻辑删除
func (l *DeleteDictCategoryLogic) DeleteDictCategory(in *dict_category.DeleteDictCategoryReq) (*dict_category.DeleteDictCategoryResp, error) {
	// 调用模型中的逻辑删除方法
	err := l.svcCtx.DictCategoryModel.LogicDelete(l.ctx, in.Id, in.DeletedBy)
	if err != nil {
		return nil, err
	}

	return &dict_category.DeleteDictCategoryResp{
		Success: true,
	}, nil
}
