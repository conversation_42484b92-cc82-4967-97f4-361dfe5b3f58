package logic

import (
	"context"

	"golang-docker-compose-main/golang/dict_category/rpc/dict_category"
	"golang-docker-compose-main/golang/dict_category/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典分类详情
func (l *GetDictCategoryLogic) GetDictCategory(in *dict_category.GetDictCategoryReq) (*dict_category.GetDictCategoryResp, error) {
	// 查询数据库
	category, err := l.svcCtx.DictCategoryModel.FindOne(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}

	// 构造返回结果
	result := &dict_category.DictCategory{
		Id:       category.Id,
		TenantId: category.TenantId,
		Name:     category.Name,
		Sort:     float32(category.Sort),
		Status:   int32(category.Status),
	}

	// 处理可空字段
	if category.CreatedBy.Valid {
		result.CreatedBy = category.CreatedBy.Int64
	}
	if category.UpdatedBy.Valid {
		result.UpdatedBy = category.UpdatedBy.Int64
	}
	if category.CreatedTime.Valid {
		result.CreatedTime = category.CreatedTime.Time.Format("2006-01-02 15:04:05")
	}
	if category.UpdatedTime.Valid {
		result.UpdatedTime = category.UpdatedTime.Time.Format("2006-01-02 15:04:05")
	}

	return &dict_category.GetDictCategoryResp{
		DictCategory: result,
	}, nil
}
