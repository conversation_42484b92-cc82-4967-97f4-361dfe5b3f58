version: "3"

tasks:
  ps:
    dir: ./
    cmds:
      - docker ps -a

  run:
    dir: ./
    cmds:
      - docker-compose up -d

  build:
    dir: ./
    cmds:
      - docker-compose up -d --build dict-item-api dict-item-rpc

  cache:
    dir: ./
    cmds:
      - docker-compose build --no-cache dict-item-api dict-item-rpc

  apidocker:
    dir: ./api
    cmds:
      - goctl docker -go dictitem.go

  rpcdocker:
    dir: ./rpc
    cmds:
      - goctl docker -go dictitem.go

  model:
    dir: ./rpc/model
    cmds:
      - goctl model mysql ddl -src="dict_item.sql" -dir="./" -c

  proto:
    dir: ./rpc
    cmds:
      - goctl rpc protoc dict_item.proto --go_out=. --go-grpc_out=. --zrpc_out=.

  api:
    dir: ./api
    cmds:
      - goctl api go -api dict_item.api -dir . -style gozero
