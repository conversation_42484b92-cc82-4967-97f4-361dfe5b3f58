package logic

import (
	"context"
	"dict/api/middleware/JWT"
	"fmt"
	"time"

	"dict/api/internal/svc"
	"dict/api/internal/types"
	"dict/rpc/dictservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictLogic {
	return &GetDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictLogic) GetDict(req *types.GetDictReq) (resp *types.GetDictResp, err error) {
	// 调用RPC服务获取字典详情
	rpcResp, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dictservice.GetDictReq{
		Id: req.Id,
	})
	if err != nil {
		return nil, fmt.Errorf("调用用户RPC服务的GetDict失败：%v", err)
	}

	// 从配置文件中获取secret 、expire
	secret := l.svcCtx.Config.Auth.AccessSecret
	expire := l.svcCtx.Config.Auth.AccessExpire
	// 生成token
	token, err := JWT.GetJwtToken(secret, time.Now().Unix(), expire, int(rpcResp.Dict.Id))

	if err != nil {
		logx.Error("token生成失败:", err)
		return nil, err
	}
	return &types.GetDictResp{
		Dict: types.Dict{
			Id:          rpcResp.Dict.Id,
			TenantId:    rpcResp.Dict.TenantId,
			CategoryId:  rpcResp.Dict.CategoryId,
			Code:        rpcResp.Dict.Code,
			Name:        rpcResp.Dict.Name,
			ShowType:    rpcResp.Dict.ShowType,
			Remark:      rpcResp.Dict.Remark,
			Status:      int(rpcResp.Dict.Status),
			CreatedBy:   rpcResp.Dict.CreatedBy,
			CreatedTime: rpcResp.Dict.CreatedTime,
		},
		Base: types.Base{
			Code:  200,
			Msg:   "获取成功",
			Token: token,
		},
	}, nil

}
