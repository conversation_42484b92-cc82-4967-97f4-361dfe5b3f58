package logic

import (
	"context"

	"dict_item/api/internal/svc"
	"dict_item/api/internal/types"
	"dict_item/rpc/dictitemclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除字典项
func NewDeleteDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictItemLogic {
	return &DeleteDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictItemLogic) DeleteDictItem(req *types.DeleteDictItemReq) (resp *types.DeleteDictItemResp, err error) {
	// 调用RPC服务逻辑删除字典项
	_, err = l.svcCtx.DictItemRpc.DeleteDictItem(l.ctx, &dictitemclient.DeleteDictItemReq{
		Id:        req.Id,
		DeletedBy: req.DeletedBy, // 直接使用用户传递的值
	})

	if err != nil {
		return &types.DeleteDictItemResp{
			Base: types.Base{
				Code: 500,
				Msg:  err.Error(),
			},
		}, nil
	}

	return &types.DeleteDictItemResp{
		Base: types.Base{
			Code: 200,
			Msg:  "success",
		},
	}, nil
}
