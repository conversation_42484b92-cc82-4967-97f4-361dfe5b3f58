@package main

import (
	"dict/api/internal/config"
	"dict/api/internal/handler"
	"dict/api/internal/svc"
	"dict/api/middleware/cros"
	"flag"
	"fmt"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/dict-api.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	// 打印配置信息进行调试
	fmt.Printf("Config: %+v\n", c)
	fmt.Printf("DictRpc: %+v\n", c.DictRpc)
	fmt.Printf("Etcd hosts: %+v\n", c.DictRpc.Etcd.Hosts)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	// 添加CORS中间件
	server.Use(cros.CrosMiddleware)

	ctx := svc.NewServiceContext(c)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
