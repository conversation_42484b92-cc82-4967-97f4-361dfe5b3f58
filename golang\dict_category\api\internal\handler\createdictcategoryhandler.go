package handler

import (
	"github.com/zeromicro/go-zero/rest/httpx"
	"golang-docker-compose-main/golang/dict_category/api/internal/logic"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"
	"golang-docker-compose-main/golang/dict_category/api/internal/types"
	"net/http"
)

func createDictCategoryHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateDictCategoryReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewCreateDictCategoryLogic(r.Context(), svcCtx)
		resp, err := l.CreateDictCategory(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
