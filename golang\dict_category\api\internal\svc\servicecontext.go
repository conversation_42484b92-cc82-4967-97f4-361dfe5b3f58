package svc

import (
	"github.com/zeromicro/go-zero/zrpc"
	"golang-docker-compose-main/golang/dict_category/api/internal/config"
	"golang-docker-compose-main/golang/dict_category/rpc/dictcategoryservice"
)

type ServiceContext struct {
	Config config.Config

	//  RPC 客户端
	DictCategoryRpc dictcategoryservice.DictCategoryService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:          c,
		DictCategoryRpc: dictcategoryservice.NewDictCategoryService(zrpc.MustNewClient(c.DictCategoryRpc)),
	}
}
