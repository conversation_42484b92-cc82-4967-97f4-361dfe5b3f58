package handler

import (
	"net/http"

	"dict_item/api/internal/logic"
	"dict_item/api/internal/svc"
	"dict_item/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 创建字典项
func CreateDictItemHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateDictItemReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewCreateDictItemLogic(r.Context(), svcCtx)
		resp, err := l.CreateDictItem(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
