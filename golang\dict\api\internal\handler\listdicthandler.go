package handler

import (
	"net/http"

	"dict/api/internal/logic"
	"dict/api/internal/svc"
	"dict/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func listDictHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListDictReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewListDictLogic(r.Context(), svcCtx)
		resp, err := l.ListDict(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
