package main

import (
	"flag"
	"fmt"
	"golang-docker-compose-main/golang/dict_category/api/middleware/cros"

	"golang-docker-compose-main/golang/dict_category/api/internal/config"
	"golang-docker-compose-main/golang/dict_category/api/internal/handler"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/dict-category-api.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	// 添加处理同源策略的cros全局中间件
	server.Use(cros.CrosMiddleware)

	ctx := svc.NewServiceContext(c)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
