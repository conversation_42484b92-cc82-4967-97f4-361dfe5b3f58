// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	"dict/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/create",
				Handler: createDictHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/delete",
				Handler: deleteDictHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/get",
				Handler: getDictHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/list",
				Handler: listDictHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/update",
				Handler: updateDict<PERSON>and<PERSON>(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)
}
