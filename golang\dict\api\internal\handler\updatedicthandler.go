package handler

import (
	"net/http"

	"dict/api/internal/logic"
	"dict/api/internal/svc"
	"dict/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func updateDictHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateDictReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewUpdateDictLogic(r.Context(), svcCtx)
		resp, err := l.UpdateDict(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
