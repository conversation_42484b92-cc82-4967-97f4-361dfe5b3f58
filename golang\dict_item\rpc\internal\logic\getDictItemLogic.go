package logic

import (
	"context"

	"dict_item/rpc/dict_item"
	"dict_item/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictItemLogic {
	return &GetDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典项详情
func (l *GetDictItemLogic) GetDictItem(in *dict_item.GetDictItemReq) (*dict_item.GetDictItemResp, error) {
	// 查询记录
	item, err := l.svcCtx.DictItemModel.FindOne(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp := &dict_item.GetDictItemResp{
		Item: &dict_item.DictItem{
			Id:     item.Id,
			DictId: item.DictId,
			Code:   item.Code,
			Name:   item.Name,
			Sort:   item.Sort,
			Status: int32(item.Status),
		},
	}

	// 处理可空字段
	if item.TenantId.Valid {
		resp.Item.TenantId = item.TenantId.Int64
	}
	if item.CategoryId.Valid {
		resp.Item.CategoryId = item.CategoryId.Int64
	}
	if item.Pid.Valid {
		resp.Item.Pid = item.Pid.Int64
	}
	if item.CreatedBy.Valid {
		resp.Item.CreatedBy = item.CreatedBy.Int64
	}
	if item.CreatedTime.Valid {
		resp.Item.CreatedTime = item.CreatedTime.Time.Format("2006-01-02 15:04:05")
	}
	if item.UpdatedTime.Valid {
		resp.Item.UpdatedTime = item.UpdatedTime.Time.Format("2006-01-02 15:04:05")
	}

	return resp, nil
}
