syntax = "proto3";

package dict_category;

option go_package = "./dict_category";

// 字典分类基本信息
message DictCategory {
  int64 id = 1;
  int64 dict_id = 10;
  int64 tenant_id = 2;
  string name = 3;
  float sort = 4;
  int32 status = 5;
  int64 created_by = 6;
  int64 updated_by = 7;
  string created_time = 8;
  string updated_time = 9;
}

// 创建字典分类请求
message CreateDictCategoryReq {
  int64 dict_id = 7;
  int64 tenant_id = 1;
  string name = 2;
  float sort = 3;
  int32 status = 4;
  int64 created_by = 5;
  string created_time = 6;
}

// 创建字典分类响应
message CreateDictCategoryResp {
  int64 id = 1;
}

// 更新字典分类请求
message UpdateDictCategoryReq {
  int64 id = 1;
  int64 dict_id = 8;
  int64 tenant_id = 2;
  string name = 3;
  float sort = 4;
  int32 status = 5;
  int64 updated_by = 6;
  string updated_time = 7;
}

// 更新字典分类响应
message UpdateDictCategoryResp {
  bool success = 1;
}

// 删除字典分类请求
message DeleteDictCategoryReq {
  int64 id = 1;
  int64 deleted_by = 2;
}

// 删除字典分类响应
message DeleteDictCategoryResp {
  bool success = 1;
}

// 获取字典分类详情请求
message GetDictCategoryReq {
  int64 id = 1;
}

// 获取字典分类详情响应
message GetDictCategoryResp {
  DictCategory dict_category = 1;
}

// 字典分类列表请求
message ListDictCategoryReq {
  int32 page = 1;
  int32 page_size = 2;
  string name = 3;
  int32 status = 4;
}

// 字典分类列表响应
message ListDictCategoryResp {
  int64 total = 1;
  repeated DictCategory list = 2;
}

// 字典分类服务
service DictCategoryService {
  // 创建字典分类
  rpc CreateDictCategory(CreateDictCategoryReq) returns(CreateDictCategoryResp);
  // 更新字典分类
  rpc UpdateDictCategory(UpdateDictCategoryReq) returns(UpdateDictCategoryResp);
  // 删除字典分类
  rpc DeleteDictCategory(DeleteDictCategoryReq) returns(DeleteDictCategoryResp);
  // 获取字典分类详情
  rpc GetDictCategory(GetDictCategoryReq) returns(GetDictCategoryResp);
  // 字典分类列表
  rpc ListDictCategory(ListDictCategoryReq) returns(ListDictCategoryResp);
} 