syntax = "v1"

info (
	title:   "字典服务API"
	desc:    "字典表的增删改查API服务"
	author:  "goctl"
	email:   "<EMAIL>"
	version: "v1"
)

type (
	// 字典基本信息
	Dict {
		Id          int64  `json:"id"`
		TenantId    int64  `json:"tenantId,optional"`
		CategoryId  int64  `json:"categoryId,optional"`
		Code        string `json:"code"`
		Name        string `json:"name"`
		ShowType    string `json:"showType"`
		Remark      string `json:"remark,optional"`
		Status      int    `json:"status,optional"`
		CreatedBy   int64  `json:"createdBy,optional"`
		CreatedTime string `json:"createdTime,optional"`
		UpdatedTime string `json:"updatedTime,optional"`
	}
	// 创建字典请求
	CreateDictReq {
		TenantId   int64  `json:"tenantId,optional"`
		CategoryId int64  `json:"categoryId,optional"`
		Code       string `json:"code"`
		Name       string `json:"name"`
		ShowType   string `json:"showType"`
		Remark     string `json:"remark,optional"`
		Status     int    `json:"status,optional"`
		CreatedBy  int64  `json:"createdBy,optional"`
	}
	Base {
		Code  int    `json:"code"`
		Msg   string `json:"msg"`
		Token string `json:"token"`
	}
	// 创建字典响应
	CreateDictResp {
		Id   int64 `json:"id"`
		Base Base  `json:"base"`
	}
	// 更新字典请求
	UpdateDictReq {
		Id         int64  `json:"id"`
		TenantId   int64  `json:"tenantId,optional"`
		CategoryId int64  `json:"categoryId,optional"`
		Code       string `json:"code,optional"`
		Name       string `json:"name,optional"`
		ShowType   string `json:"showType,optional"`
		Remark     string `json:"remark,optional"`
		Status     int    `json:"status,optional"`
	}
	// 更新字典响应
	UpdateDictResp {
		Success bool `json:"success"`
		Base    Base `json:"base"`
	}
	// 删除字典请求
	DeleteDictReq {
		Id        int64 `json:"id"`
		DeletedBy int64 `json:"deletedBy"`
	}
	// 删除字典响应
	DeleteDictResp {
		Success bool `json:"success"`
		Base    Base `json:"base"`
	}
	// 获取字典详情请求
	GetDictReq {
		Id int64 `json:"id"`
	}
	// 获取字典详情响应
	GetDictResp {
		Dict Dict `json:"dict"`
		Base Base `json:"base"`
	}
	// 字典列表请求
	ListDictReq {
		Page     int `json:"page,optional"`
		PageSize int `json:"pageSize,optional"`
	}
	// 字典列表响应
	ListDictResp {
		Total int64  `json:"total"`
		List  []Dict `json:"list"`
		Base  Base   `json:"base"`
	}
)

@server (
	jwt: Auth // 开启 jwt 认证
)
service dict-api {
	@handler createDict
	post /api/dict/create (CreateDictReq) returns (CreateDictResp)

	@handler updateDict
	post /api/dict/update (UpdateDictReq) returns (UpdateDictResp)

	@handler deleteDict
	post /api/dict/delete (DeleteDictReq) returns (DeleteDictResp)

	@handler getDict
	post /api/dict/get (GetDictReq) returns (GetDictResp)

	@handler listDict
	post /api/dict/list (ListDictReq) returns (ListDictResp)
}

