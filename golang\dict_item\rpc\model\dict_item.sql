CREATE TABLE `dict_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '归属租户',
  `dict_id` bigint(20) NOT NULL COMMENT '归属字典',
  `category_id` bigint(20) DEFAULT NULL COMMENT '归属分类',
  `code` varchar(128) NOT NULL COMMENT '编码',
  `name` varchar(255) NOT NULL COMMENT '显示名称',
  `pid` bigint(20) DEFAULT NULL COMMENT '所属上级',
  `sort` float DEFAULT '99' COMMENT '排序',
  `status` tinyint(2) DEFAULT '1' COMMENT '数据状态',
  `created_by` bigint(20) DEFAULT NULL COMMENT '添加人',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '编辑人',
  `deleted_by` bigint(20) DEFAULT NULL COMMENT '删除人',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '添加时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_time` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_dict_id` (`dict_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_pid` (`pid`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_code` (`code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典选项表'; 