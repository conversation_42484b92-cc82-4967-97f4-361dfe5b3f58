// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: rpc/dict.proto

package dict

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 字典基本信息
type Dict struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TenantId      int64                  `protobuf:"varint,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	ShowType      string                 `protobuf:"bytes,6,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	Remark        string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	CreatedBy     int64                  `protobuf:"varint,9,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,10,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,11,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dict) Reset() {
	*x = Dict{}
	mi := &file_rpc_dict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dict) ProtoMessage() {}

func (x *Dict) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dict.ProtoReflect.Descriptor instead.
func (*Dict) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{0}
}

func (x *Dict) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dict) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *Dict) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Dict) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Dict) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Dict) GetShowType() string {
	if x != nil {
		return x.ShowType
	}
	return ""
}

func (x *Dict) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Dict) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Dict) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *Dict) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *Dict) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 创建字典请求
type CreateDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      int64                  `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	ShowType      string                 `protobuf:"bytes,5,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	Remark        string                 `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	CreatedBy     int64                  `protobuf:"varint,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,9,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictReq) Reset() {
	*x = CreateDictReq{}
	mi := &file_rpc_dict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictReq) ProtoMessage() {}

func (x *CreateDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictReq.ProtoReflect.Descriptor instead.
func (*CreateDictReq) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDictReq) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *CreateDictReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CreateDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CreateDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictReq) GetShowType() string {
	if x != nil {
		return x.ShowType
	}
	return ""
}

func (x *CreateDictReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreateDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateDictReq) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *CreateDictReq) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

// 创建字典响应
type CreateDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictResp) Reset() {
	*x = CreateDictResp{}
	mi := &file_rpc_dict_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictResp) ProtoMessage() {}

func (x *CreateDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictResp.ProtoReflect.Descriptor instead.
func (*CreateDictResp) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDictResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 更新字典请求
type UpdateDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TenantId      int64                  `protobuf:"varint,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	ShowType      string                 `protobuf:"bytes,6,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	Remark        string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	UpdatedBy     int64                  `protobuf:"varint,9,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,10,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictReq) Reset() {
	*x = UpdateDictReq{}
	mi := &file_rpc_dict_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictReq) ProtoMessage() {}

func (x *UpdateDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictReq.ProtoReflect.Descriptor instead.
func (*UpdateDictReq) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictReq) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *UpdateDictReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UpdateDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UpdateDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictReq) GetShowType() string {
	if x != nil {
		return x.ShowType
	}
	return ""
}

func (x *UpdateDictReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpdateDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateDictReq) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *UpdateDictReq) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 更新字典响应
type UpdateDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictResp) Reset() {
	*x = UpdateDictResp{}
	mi := &file_rpc_dict_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictResp) ProtoMessage() {}

func (x *UpdateDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictResp.ProtoReflect.Descriptor instead.
func (*UpdateDictResp) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDictResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 删除字典请求
type DeleteDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     int64                  `protobuf:"varint,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictReq) Reset() {
	*x = DeleteDictReq{}
	mi := &file_rpc_dict_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictReq) ProtoMessage() {}

func (x *DeleteDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictReq.ProtoReflect.Descriptor instead.
func (*DeleteDictReq) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteDictReq) GetDeletedBy() int64 {
	if x != nil {
		return x.DeletedBy
	}
	return 0
}

// 删除字典响应
type DeleteDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictResp) Reset() {
	*x = DeleteDictResp{}
	mi := &file_rpc_dict_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictResp) ProtoMessage() {}

func (x *DeleteDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictResp.ProtoReflect.Descriptor instead.
func (*DeleteDictResp) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteDictResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 获取字典详情请求
type GetDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictReq) Reset() {
	*x = GetDictReq{}
	mi := &file_rpc_dict_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictReq) ProtoMessage() {}

func (x *GetDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictReq.ProtoReflect.Descriptor instead.
func (*GetDictReq) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{7}
}

func (x *GetDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典详情响应
type GetDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dict          *Dict                  `protobuf:"bytes,1,opt,name=dict,proto3" json:"dict,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictResp) Reset() {
	*x = GetDictResp{}
	mi := &file_rpc_dict_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictResp) ProtoMessage() {}

func (x *GetDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictResp.ProtoReflect.Descriptor instead.
func (*GetDictResp) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{8}
}

func (x *GetDictResp) GetDict() *Dict {
	if x != nil {
		return x.Dict
	}
	return nil
}

// 字典列表请求
type ListDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictReq) Reset() {
	*x = ListDictReq{}
	mi := &file_rpc_dict_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictReq) ProtoMessage() {}

func (x *ListDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictReq.ProtoReflect.Descriptor instead.
func (*ListDictReq) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{9}
}

func (x *ListDictReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ListDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 字典列表响应
type ListDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*Dict                `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictResp) Reset() {
	*x = ListDictResp{}
	mi := &file_rpc_dict_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictResp) ProtoMessage() {}

func (x *ListDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dict_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictResp.ProtoReflect.Descriptor instead.
func (*ListDictResp) Descriptor() ([]byte, []int) {
	return file_rpc_dict_proto_rawDescGZIP(), []int{10}
}

func (x *ListDictResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictResp) GetList() []*Dict {
	if x != nil {
		return x.List
	}
	return nil
}

var File_rpc_dict_proto protoreflect.FileDescriptor

const file_rpc_dict_proto_rawDesc = "" +
	"\n" +
	"\x0erpc/dict.proto\x12\x04dict\"\xae\x02\n" +
	"\x04Dict\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\ttenant_id\x18\x02 \x01(\x03R\btenantId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x1b\n" +
	"\tshow_type\x18\x06 \x01(\tR\bshowType\x12\x16\n" +
	"\x06remark\x18\a \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\b \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_by\x18\t \x01(\x03R\tcreatedBy\x12!\n" +
	"\fcreated_time\x18\n" +
	" \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\v \x01(\tR\vupdatedTime\"\x84\x02\n" +
	"\rCreateDictReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1b\n" +
	"\tshow_type\x18\x05 \x01(\tR\bshowType\x12\x16\n" +
	"\x06remark\x18\x06 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\a \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_by\x18\b \x01(\x03R\tcreatedBy\x12!\n" +
	"\fcreated_time\x18\t \x01(\tR\vcreatedTime\" \n" +
	"\x0eCreateDictResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x94\x02\n" +
	"\rUpdateDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\ttenant_id\x18\x02 \x01(\x03R\btenantId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x1b\n" +
	"\tshow_type\x18\x06 \x01(\tR\bshowType\x12\x16\n" +
	"\x06remark\x18\a \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\b \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"updated_by\x18\t \x01(\x03R\tupdatedBy\x12!\n" +
	"\fupdated_time\x18\n" +
	" \x01(\tR\vupdatedTime\"*\n" +
	"\x0eUpdateDictResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\">\n" +
	"\rDeleteDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\x03R\tdeletedBy\"*\n" +
	"\x0eDeleteDictResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\x1c\n" +
	"\n" +
	"GetDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"-\n" +
	"\vGetDictResp\x12\x1e\n" +
	"\x04dict\x18\x01 \x01(\v2\n" +
	".dict.DictR\x04dict\"~\n" +
	"\vListDictReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\"D\n" +
	"\fListDictResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12\x1e\n" +
	"\x04list\x18\x02 \x03(\v2\n" +
	".dict.DictR\x04list2\x9b\x02\n" +
	"\vDictService\x127\n" +
	"\n" +
	"CreateDict\x12\x13.dict.CreateDictReq\x1a\x14.dict.CreateDictResp\x127\n" +
	"\n" +
	"UpdateDict\x12\x13.dict.UpdateDictReq\x1a\x14.dict.UpdateDictResp\x127\n" +
	"\n" +
	"DeleteDict\x12\x13.dict.DeleteDictReq\x1a\x14.dict.DeleteDictResp\x12.\n" +
	"\aGetDict\x12\x10.dict.GetDictReq\x1a\x11.dict.GetDictResp\x121\n" +
	"\bListDict\x12\x11.dict.ListDictReq\x1a\x12.dict.ListDictRespB\bZ\x06./dictb\x06proto3"

var (
	file_rpc_dict_proto_rawDescOnce sync.Once
	file_rpc_dict_proto_rawDescData []byte
)

func file_rpc_dict_proto_rawDescGZIP() []byte {
	file_rpc_dict_proto_rawDescOnce.Do(func() {
		file_rpc_dict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dict_proto_rawDesc), len(file_rpc_dict_proto_rawDesc)))
	})
	return file_rpc_dict_proto_rawDescData
}

var file_rpc_dict_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_rpc_dict_proto_goTypes = []any{
	(*Dict)(nil),           // 0: dict.Dict
	(*CreateDictReq)(nil),  // 1: dict.CreateDictReq
	(*CreateDictResp)(nil), // 2: dict.CreateDictResp
	(*UpdateDictReq)(nil),  // 3: dict.UpdateDictReq
	(*UpdateDictResp)(nil), // 4: dict.UpdateDictResp
	(*DeleteDictReq)(nil),  // 5: dict.DeleteDictReq
	(*DeleteDictResp)(nil), // 6: dict.DeleteDictResp
	(*GetDictReq)(nil),     // 7: dict.GetDictReq
	(*GetDictResp)(nil),    // 8: dict.GetDictResp
	(*ListDictReq)(nil),    // 9: dict.ListDictReq
	(*ListDictResp)(nil),   // 10: dict.ListDictResp
}
var file_rpc_dict_proto_depIdxs = []int32{
	0,  // 0: dict.GetDictResp.dict:type_name -> dict.Dict
	0,  // 1: dict.ListDictResp.list:type_name -> dict.Dict
	1,  // 2: dict.DictService.CreateDict:input_type -> dict.CreateDictReq
	3,  // 3: dict.DictService.UpdateDict:input_type -> dict.UpdateDictReq
	5,  // 4: dict.DictService.DeleteDict:input_type -> dict.DeleteDictReq
	7,  // 5: dict.DictService.GetDict:input_type -> dict.GetDictReq
	9,  // 6: dict.DictService.ListDict:input_type -> dict.ListDictReq
	2,  // 7: dict.DictService.CreateDict:output_type -> dict.CreateDictResp
	4,  // 8: dict.DictService.UpdateDict:output_type -> dict.UpdateDictResp
	6,  // 9: dict.DictService.DeleteDict:output_type -> dict.DeleteDictResp
	8,  // 10: dict.DictService.GetDict:output_type -> dict.GetDictResp
	10, // 11: dict.DictService.ListDict:output_type -> dict.ListDictResp
	7,  // [7:12] is the sub-list for method output_type
	2,  // [2:7] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_rpc_dict_proto_init() }
func file_rpc_dict_proto_init() {
	if File_rpc_dict_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dict_proto_rawDesc), len(file_rpc_dict_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_dict_proto_goTypes,
		DependencyIndexes: file_rpc_dict_proto_depIdxs,
		MessageInfos:      file_rpc_dict_proto_msgTypes,
	}.Build()
	File_rpc_dict_proto = out.File
	file_rpc_dict_proto_goTypes = nil
	file_rpc_dict_proto_depIdxs = nil
}
