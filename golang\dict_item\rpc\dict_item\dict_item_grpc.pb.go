// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.0
// source: rpc/dict_item.proto

package dict_item

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DictItemService_CreateDictItem_FullMethodName = "/dict_item.DictItemService/CreateDictItem"
	DictItemService_UpdateDictItem_FullMethodName = "/dict_item.DictItemService/UpdateDictItem"
	DictItemService_DeleteDictItem_FullMethodName = "/dict_item.DictItemService/DeleteDictItem"
	DictItemService_GetDictItem_FullMethodName    = "/dict_item.DictItemService/GetDictItem"
	DictItemService_ListDictItem_FullMethodName   = "/dict_item.DictItemService/ListDictItem"
)

// DictItemServiceClient is the client API for DictItemService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 字典项服务
type DictItemServiceClient interface {
	// 创建字典项
	CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error)
	// 更新字典项
	UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error)
	// 删除字典项
	DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error)
	// 获取字典项详情
	GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error)
	// 获取字典项列表
	ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error)
}

type dictItemServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDictItemServiceClient(cc grpc.ClientConnInterface) DictItemServiceClient {
	return &dictItemServiceClient{cc}
}

func (c *dictItemServiceClient) CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDictItemResp)
	err := c.cc.Invoke(ctx, DictItemService_CreateDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictItemServiceClient) UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDictItemResp)
	err := c.cc.Invoke(ctx, DictItemService_UpdateDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictItemServiceClient) DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDictItemResp)
	err := c.cc.Invoke(ctx, DictItemService_DeleteDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictItemServiceClient) GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDictItemResp)
	err := c.cc.Invoke(ctx, DictItemService_GetDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictItemServiceClient) ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDictItemResp)
	err := c.cc.Invoke(ctx, DictItemService_ListDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DictItemServiceServer is the server API for DictItemService service.
// All implementations must embed UnimplementedDictItemServiceServer
// for forward compatibility.
//
// 字典项服务
type DictItemServiceServer interface {
	// 创建字典项
	CreateDictItem(context.Context, *CreateDictItemReq) (*CreateDictItemResp, error)
	// 更新字典项
	UpdateDictItem(context.Context, *UpdateDictItemReq) (*UpdateDictItemResp, error)
	// 删除字典项
	DeleteDictItem(context.Context, *DeleteDictItemReq) (*DeleteDictItemResp, error)
	// 获取字典项详情
	GetDictItem(context.Context, *GetDictItemReq) (*GetDictItemResp, error)
	// 获取字典项列表
	ListDictItem(context.Context, *ListDictItemReq) (*ListDictItemResp, error)
	mustEmbedUnimplementedDictItemServiceServer()
}

// UnimplementedDictItemServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDictItemServiceServer struct{}

func (UnimplementedDictItemServiceServer) CreateDictItem(context.Context, *CreateDictItemReq) (*CreateDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDictItem not implemented")
}
func (UnimplementedDictItemServiceServer) UpdateDictItem(context.Context, *UpdateDictItemReq) (*UpdateDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDictItem not implemented")
}
func (UnimplementedDictItemServiceServer) DeleteDictItem(context.Context, *DeleteDictItemReq) (*DeleteDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDictItem not implemented")
}
func (UnimplementedDictItemServiceServer) GetDictItem(context.Context, *GetDictItemReq) (*GetDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDictItem not implemented")
}
func (UnimplementedDictItemServiceServer) ListDictItem(context.Context, *ListDictItemReq) (*ListDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDictItem not implemented")
}
func (UnimplementedDictItemServiceServer) mustEmbedUnimplementedDictItemServiceServer() {}
func (UnimplementedDictItemServiceServer) testEmbeddedByValue()                         {}

// UnsafeDictItemServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DictItemServiceServer will
// result in compilation errors.
type UnsafeDictItemServiceServer interface {
	mustEmbedUnimplementedDictItemServiceServer()
}

func RegisterDictItemServiceServer(s grpc.ServiceRegistrar, srv DictItemServiceServer) {
	// If the following call pancis, it indicates UnimplementedDictItemServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DictItemService_ServiceDesc, srv)
}

func _DictItemService_CreateDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictItemServiceServer).CreateDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictItemService_CreateDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictItemServiceServer).CreateDictItem(ctx, req.(*CreateDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictItemService_UpdateDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictItemServiceServer).UpdateDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictItemService_UpdateDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictItemServiceServer).UpdateDictItem(ctx, req.(*UpdateDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictItemService_DeleteDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictItemServiceServer).DeleteDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictItemService_DeleteDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictItemServiceServer).DeleteDictItem(ctx, req.(*DeleteDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictItemService_GetDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictItemServiceServer).GetDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictItemService_GetDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictItemServiceServer).GetDictItem(ctx, req.(*GetDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictItemService_ListDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictItemServiceServer).ListDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictItemService_ListDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictItemServiceServer).ListDictItem(ctx, req.(*ListDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DictItemService_ServiceDesc is the grpc.ServiceDesc for DictItemService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DictItemService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dict_item.DictItemService",
	HandlerType: (*DictItemServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDictItem",
			Handler:    _DictItemService_CreateDictItem_Handler,
		},
		{
			MethodName: "UpdateDictItem",
			Handler:    _DictItemService_UpdateDictItem_Handler,
		},
		{
			MethodName: "DeleteDictItem",
			Handler:    _DictItemService_DeleteDictItem_Handler,
		},
		{
			MethodName: "GetDictItem",
			Handler:    _DictItemService_GetDictItem_Handler,
		},
		{
			MethodName: "ListDictItem",
			Handler:    _DictItemService_ListDictItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/dict_item.proto",
}
