package logic

import (
	"context"
	"fmt"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"
	"golang-docker-compose-main/golang/dict_category/api/internal/types"
	"golang-docker-compose-main/golang/dict_category/rpc/dictcategoryservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictCategoryLogic) DeleteDictCategory(req *types.DeleteDictCategoryReq) (resp *types.DeleteDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.DictCategoryRpc.DeleteDictCategory(l.ctx, &dictcategoryservice.DeleteDictCategoryReq{
		Id:        req.Id,
		DeletedBy: req.DeletedBy,
	})
	if err != nil {
		return nil, fmt.Errorf("调用RPC的DeleteDictCategory失败：%v", err)
	}

	return &types.DeleteDictCategoryResp{
		Success: rpcResp.Success,
		Base: types.Base{
			Code: 200,
			Msg:  "调用RPC的DeleteDictCategory字典分类删除成功",
		},
	}, nil
}
