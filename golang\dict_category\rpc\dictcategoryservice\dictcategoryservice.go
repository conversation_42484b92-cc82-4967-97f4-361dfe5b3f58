// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict_category.proto

package dictcategoryservice

import (
	"context"

	"golang-docker-compose-main/golang/dict_category/rpc/dict_category"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateDictCategoryReq  = dict_category.CreateDictCategoryReq
	CreateDictCategoryResp = dict_category.CreateDictCategoryResp
	DeleteDictCategoryReq  = dict_category.DeleteDictCategoryReq
	DeleteDictCategoryResp = dict_category.DeleteDictCategoryResp
	DictCategory           = dict_category.DictCategory
	GetDictCategoryReq     = dict_category.GetDictCategoryReq
	GetDictCategoryResp    = dict_category.GetDictCategoryResp
	ListDictCategoryReq    = dict_category.ListDictCategoryReq
	ListDictCategoryResp   = dict_category.ListDictCategoryResp
	UpdateDictCategoryReq  = dict_category.UpdateDictCategoryReq
	UpdateDictCategoryResp = dict_category.UpdateDictCategoryResp

	DictCategoryService interface {
		// 创建字典分类
		CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error)
		// 更新字典分类
		UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error)
		// 删除字典分类
		DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error)
		// 获取字典分类详情
		GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error)
		// 字典分类列表
		ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error)
	}

	defaultDictCategoryService struct {
		cli zrpc.Client
	}
)

func NewDictCategoryService(cli zrpc.Client) DictCategoryService {
	return &defaultDictCategoryService{
		cli: cli,
	}
}

// 创建字典分类
func (m *defaultDictCategoryService) CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error) {
	client := dict_category.NewDictCategoryServiceClient(m.cli.Conn())
	return client.CreateDictCategory(ctx, in, opts...)
}

// 更新字典分类
func (m *defaultDictCategoryService) UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error) {
	client := dict_category.NewDictCategoryServiceClient(m.cli.Conn())
	return client.UpdateDictCategory(ctx, in, opts...)
}

// 删除字典分类
func (m *defaultDictCategoryService) DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error) {
	client := dict_category.NewDictCategoryServiceClient(m.cli.Conn())
	return client.DeleteDictCategory(ctx, in, opts...)
}

// 获取字典分类详情
func (m *defaultDictCategoryService) GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error) {
	client := dict_category.NewDictCategoryServiceClient(m.cli.Conn())
	return client.GetDictCategory(ctx, in, opts...)
}

// 字典分类列表
func (m *defaultDictCategoryService) ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error) {
	client := dict_category.NewDictCategoryServiceClient(m.cli.Conn())
	return client.ListDictCategory(ctx, in, opts...)
}
