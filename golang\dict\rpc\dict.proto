syntax = "proto3";

package dict;

option go_package = "./dict";

// 字典基本信息
message Dict {
  int64 id = 1;
  int64 tenant_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  string show_type = 6;
  string remark = 7;
  int32 status = 8;
  int64 created_by = 9;
  string created_time = 10;
  string updated_time = 11;
}

// 创建字典请求
message CreateDictReq {
  int64 tenant_id = 1;
  int64 category_id = 2;
  string code = 3;
  string name = 4;
  string show_type = 5;
  string remark = 6;
  int32 status = 7;
  int64 created_by = 8;
  string created_time = 9;
}

// 创建字典响应
message CreateDictResp {
  int64 id = 1;
}

// 更新字典请求
message UpdateDictReq {
  int64 id = 1;
  int64 tenant_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  string show_type = 6;
  string remark = 7;
  int32 status = 8;
  int64 updated_by = 9;
  string updated_time = 10;
}

// 更新字典响应
message UpdateDictResp {
  bool success = 1;
}

// 删除字典请求
message DeleteDictReq {
  int64 id = 1;
  int64 deleted_by = 2;
}

// 删除字典响应
message DeleteDictResp {
  bool success = 1;
}

// 获取字典详情请求
message GetDictReq {
  int64 id = 1;
}

// 获取字典详情响应
message GetDictResp {
  Dict dict = 1;
}

// 字典列表请求
message ListDictReq {
  int32 page = 1;
  int32 page_size = 2;
}

// 字典列表响应
message ListDictResp {
  int64 total = 1;
  repeated Dict list = 2;
}

// 字典服务
service DictService {
  // 创建字典
  rpc CreateDict(CreateDictReq) returns(CreateDictResp);
  // 更新字典
  rpc UpdateDict(UpdateDictReq) returns(UpdateDictResp);
  // 删除字典
  rpc DeleteDict(DeleteDictReq) returns(DeleteDictResp);
  // 获取字典详情
  rpc GetDict(GetDictReq) returns(GetDictResp);
  // 字典列表
  rpc ListDict(ListDictReq) returns(ListDictResp);
} 