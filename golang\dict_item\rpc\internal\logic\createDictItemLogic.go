package logic

import (
	"context"
	"database/sql"
	"time"

	"dict_item/rpc/dict_item"
	"dict_item/rpc/internal/svc"
	"dict_item/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典项
func (l *CreateDictItemLogic) CreateDictItem(in *dict_item.CreateDictItemReq) (*dict_item.CreateDictItemResp, error) {
	// 构建数据模型
	now := time.Now()
	item := &model.DictItem{
		DictId:      in.DictId,
		Code:        in.Code,
		Name:        in.Name,
		Status:      int64(in.Status),
		Sort:        in.Sort,
		CreatedTime: sql.NullTime{Time: now, Valid: true},
	}

	// 设置可选字段
	if in.TenantId > 0 {
		item.TenantId = sql.NullInt64{Int64: in.TenantId, Valid: true}
	}
	if in.CategoryId > 0 {
		item.CategoryId = sql.NullInt64{Int64: in.CategoryId, Valid: true}
	}
	if in.Pid > 0 {
		item.Pid = sql.NullInt64{Int64: in.Pid, Valid: true}
	} else {
		// 默认设置为0
		item.Pid = sql.NullInt64{Int64: 0, Valid: true}
	}
	if in.CreatedBy > 0 {
		item.CreatedBy = sql.NullInt64{Int64: in.CreatedBy, Valid: true}
	}

	// 插入数据
	result, err := l.svcCtx.DictItemModel.Insert(l.ctx, item)
	if err != nil {
		return nil, err
	}

	// 获取插入ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &dict_item.CreateDictItemResp{
		Id: id,
	}, nil
}
