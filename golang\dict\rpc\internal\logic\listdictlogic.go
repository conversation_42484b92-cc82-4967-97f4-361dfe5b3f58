package logic

import (
	"context"
	"dict/rpc/dict"
	"dict/rpc/internal/svc"
	"fmt"
	"mp/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictLogic {
	return &ListDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典列表
func (l *ListDictLogic) ListDict(in *dict.ListDictReq) (*dict.ListDictResp, error) {
	// 定义分页参数
	page := int(in.Page)
	if page <= 0 {
		page = 1
	}
	pageSize := int(in.PageSize)
	if pageSize <= 0 {
		pageSize = 10 // 默认每页10条记录
	}

	offset := (page - 1) * pageSize

	// 查询记录总数
	var total int64
	l.svcCtx.DB.Model(&model.Dicts{}).Count(&total)

	// 分页查询数据
	var dicts []model.Dicts
	result := l.svcCtx.DB.Offset(offset).Limit(pageSize).Find(&dicts)
	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var list []*dict.Dict
	for _, d := range dicts {
		dictItem := &dict.Dict{
			Id:          int64(d.ID),
			TenantId:    int64(d.TenantId),
			CategoryId:  int64(d.CategoryId),
			Code:        fmt.Sprintf("%d", d.Code),
			Name:        d.Name,
			ShowType:    d.ShowType,
			Remark:      d.Remark,
			Status:      int32(d.Status),
			CreatedBy:   int64(d.CreatedBy),
			CreatedTime: d.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 如果有更新时间，也转换
		if !d.UpdatedAt.IsZero() {
			dictItem.UpdatedTime = d.UpdatedAt.Format("2006-01-02 15:04:05")
		}

		list = append(list, dictItem)
	}

	return &dict.ListDictResp{
		Total: int64(total),
		List:  list,
	}, nil
}
