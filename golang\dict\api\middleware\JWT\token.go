package JWT

import "github.com/golang-jwt/jwt/v4"

// 进行 token 配置
func GetJwtToken(secretKey string, iat, seconds int64, key int) (string, error) {
	// 创建一个 MapClaims 类型的声明
	claims := make(jwt.MapClaims)
	// 计算过期时间
	claims["exp"] = iat + seconds // 设置 JWT 的过期时间 (exp) ,通常需要以一个 UNIX 的时间戳
	claims["iat"] = iat           // 设置签发时间（iat）
	claims["key"] = key           // 自定义的负载（playload）,可以设置为任何信息，例如用户名，用户ID等

	// 创建一个新的 JWT Token
	token := jwt.New(jwt.SigningMethodHS256) // 使用 HMAC SHA356 签名方法创建新的 JWT

	// 将声明分配给 JWT
	token.Claims = claims

	// 使用 secretKey 签名 JWT 并返回生成的字符串和错误
	return token.SignedString([]byte(secretKey))
}
