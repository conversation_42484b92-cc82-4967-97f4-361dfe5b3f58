// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	"golang-docker-compose-main/golang/dict_category/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/category/create",
				Handler: createDictCategoryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/category/delete",
				Handler: deleteDictCategoryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/category/get",
				Handler: getDictCategoryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/category/list",
				Handler: listDict<PERSON>ategoryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/dict/category/update",
				Handler: updateDict<PERSON>ate<PERSON>y<PERSON>and<PERSON>(serverCtx),
			},
		},
	)
}
