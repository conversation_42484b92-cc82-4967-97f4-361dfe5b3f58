package logic

import (
	"context"
	"database/sql"
	"time"

	"golang-docker-compose-main/golang/dict_category/rpc/dict_category"
	"golang-docker-compose-main/golang/dict_category/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典分类
func (l *UpdateDictCategoryLogic) UpdateDictCategory(in *dict_category.UpdateDictCategoryReq) (*dict_category.UpdateDictCategoryResp, error) {
	// 先查询记录是否存在
	category, err := l.svcCtx.DictCategoryModel.FindOne(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	now := time.Now()
	if in.TenantId > 0 {
		category.TenantId = in.TenantId
	}
	if in.Name != "" {
		category.Name = in.Name
	}
	category.Sort = float64(in.Sort)
	if in.Status > 0 {
		category.Status = int64(in.Status)
	}
	if in.UpdatedBy > 0 {
		category.UpdatedBy = sql.NullInt64{Int64: in.UpdatedBy, Valid: true}
	}
	category.UpdatedTime = sql.NullTime{Time: now, Valid: true}

	// 执行更新
	err = l.svcCtx.DictCategoryModel.Update(l.ctx, category)
	if err != nil {
		return nil, err
	}

	return &dict_category.UpdateDictCategoryResp{
		Success: true,
	}, nil
}
