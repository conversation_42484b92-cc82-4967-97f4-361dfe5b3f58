// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	dictCategoryFieldNames          = builder.RawFieldNames(&DictCategory{})
	dictCategoryRows                = strings.Join(dictCategoryFieldNames, ",")
	dictCategoryRowsExpectAutoSet   = strings.Join(stringx.Remove(dictCategoryFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	dictCategoryRowsWithPlaceHolder = strings.Join(stringx.Remove(dictCategoryFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheDictCategoryIdPrefix = "cache:dictCategory:id:"
)

type (
	dictCategoryModel interface {
		Insert(ctx context.Context, data *DictCategory) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*DictCategory, error)
		Update(ctx context.Context, data *DictCategory) error
		Delete(ctx context.Context, id int64) error
	}

	defaultDictCategoryModel struct {
		sqlc.CachedConn
		table string
	}

	DictCategory struct {
		Id          int64         `db:"id"`
		DictId      int64         `db:"dict_id"`      // 字典ID
		TenantId    int64         `db:"tenant_id"`    // 租户ID
		Name        string        `db:"name"`         // 字段分类名称
		Sort        float64       `db:"sort"`         // 排序值
		Status      int64         `db:"status"`       // 数据状态
		CreatedBy   sql.NullInt64 `db:"created_by"`   // 操作人
		UpdatedBy   sql.NullInt64 `db:"updated_by"`   // 编辑人
		CreatedTime sql.NullTime  `db:"created_time"` // 添加时间
		UpdatedTime sql.NullTime  `db:"updated_time"` // 修改时间
		DeletedTime sql.NullTime  `db:"deleted_time"` // 删除时间
	}
)

func newDictCategoryModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultDictCategoryModel {
	return &defaultDictCategoryModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`dict_category`",
	}
}

func (m *defaultDictCategoryModel) Delete(ctx context.Context, id int64) error {
	dictCategoryIdKey := fmt.Sprintf("%s%v", cacheDictCategoryIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, dictCategoryIdKey)
	return err
}

func (m *defaultDictCategoryModel) FindOne(ctx context.Context, id int64) (*DictCategory, error) {
	dictCategoryIdKey := fmt.Sprintf("%s%v", cacheDictCategoryIdPrefix, id)
	var resp DictCategory
	err := m.QueryRowCtx(ctx, &resp, dictCategoryIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", dictCategoryRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultDictCategoryModel) Insert(ctx context.Context, data *DictCategory) (sql.Result, error) {
	dictCategoryIdKey := fmt.Sprintf("%s%v", cacheDictCategoryIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, dictCategoryRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.DictId, data.TenantId, data.Name, data.Sort, data.Status, data.CreatedBy, data.UpdatedBy, data.CreatedTime, data.UpdatedTime, data.DeletedTime)
	}, dictCategoryIdKey)
	return ret, err
}

func (m *defaultDictCategoryModel) Update(ctx context.Context, data *DictCategory) error {
	dictCategoryIdKey := fmt.Sprintf("%s%v", cacheDictCategoryIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, dictCategoryRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.DictId, data.TenantId, data.Name, data.Sort, data.Status, data.CreatedBy, data.UpdatedBy, data.CreatedTime, data.UpdatedTime, data.DeletedTime, data.Id)
	}, dictCategoryIdKey)
	return err
}

func (m *defaultDictCategoryModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheDictCategoryIdPrefix, primary)
}

func (m *defaultDictCategoryModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", dictCategoryRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultDictCategoryModel) tableName() string {
	return m.table
}
