package logic

import (
	"context"
	"dict/api/middleware/JWT"
	"time"

	"dict/api/internal/svc"
	"dict/api/internal/types"
	"dict/rpc/dictservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictLogic {
	return &ListDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictLogic) ListDict(req *types.ListDictReq) (resp *types.ListDictResp, err error) {
	rpcResp, err := l.svcCtx.DictRpc.ListDict(l.ctx, &dictservice.ListDictReq{
		Page:     int32(req.<PERSON>),
		PageSize: int32(req.PageSize),
	})
	if err != nil {
		return nil, err
	}
	var list []types.Dict
	for _, d := range rpcResp.List {
		list = append(list, types.Dict{
			Id:          d.Id,
			TenantId:    d.TenantId,
			CategoryId:  d.CategoryId,
			Code:        d.Code,
			Name:        d.Name,
			ShowType:    d.ShowType,
			Remark:      d.Remark,
			Status:      int(d.Status),
			CreatedBy:   d.CreatedBy,
			CreatedTime: d.CreatedTime,
			UpdatedTime: d.UpdatedTime,
		})
	}
	// 从配置文件中获取secret 、expire
	secret := l.svcCtx.Config.Auth.AccessSecret
	expire := l.svcCtx.Config.Auth.AccessExpire
	// 生成token
	token, err := JWT.GetJwtToken(secret, time.Now().Unix(), expire, int(rpcResp.List[0].Id))

	if err != nil {
		logx.Error("token生成失败:", err)
		return nil, err
	}
	return &types.ListDictResp{
		Total: rpcResp.Total,
		List:  list,
		Base: types.Base{
			Code:  200,
			Msg:   "success",
			Token: token,
		},
	}, nil
}
