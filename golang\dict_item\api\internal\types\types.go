// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type Base struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type CreateDictItemReq struct {
	TenantId   int64   `json:"tenantId,optional"`
	DictId     int64   `json:"dictId"`
	CategoryId int64   `json:"categoryId"`
	Code       string  `json:"code"`
	Name       string  `json:"name"`
	Pid        int64   `json:"pid,optional"`
	Sort       float64 `json:"sort,optional"`
	Status     int     `json:"status,optional"`
	CreatedBy  int64   `json:"createdBy"` // 添加人ID
}

type CreateDictItemResp struct {
	Base
	Id int64 `json:"id"`
}

type DeleteDictItemReq struct {
	Id        int64 `json:"id"`
	DeletedBy int64 `json:"deletedBy"` // 删除人ID
}

type DeleteDictItemResp struct {
	Base
}

type DictItem struct {
	Id          int64   `json:"id"`
	TenantId    int64   `json:"tenantId"`
	DictId      int64   `json:"dictId"`
	CategoryId  int64   `json:"categoryId"`
	Code        string  `json:"code"`
	Name        string  `json:"name"`
	Pid         int64   `json:"pid"`
	Sort        float64 `json:"sort"`
	Status      int     `json:"status"`
	CreatedBy   int64   `json:"createdBy"`
	CreatedTime string  `json:"createdTime"`
	UpdatedTime string  `json:"updatedTime"`
}

type GetDictItemReq struct {
	Id int64 `json:"id"`
}

type GetDictItemResp struct {
	Base
	Data DictItem `json:"data"`
}

type ListDictItemReq struct {
	Page       int    `json:"page,optional"`
	PageSize   int    `json:"pageSize,optional"`
	DictId     int64  `json:"dictId,optional"`
	CategoryId int64  `json:"categoryId,optional"`
	Code       string `json:"code,optional"`
	Name       string `json:"name,optional"`
	Status     int    `json:"status,optional"`
}

type ListDictItemResp struct {
	Base
	Total int64      `json:"total"`
	List  []DictItem `json:"list"`
}

type UpdateDictItemReq struct {
	Id         int64   `json:"id"`
	TenantId   int64   `json:"tenantId,optional"`
	DictId     int64   `json:"dictId,optional"`
	CategoryId int64   `json:"categoryId,optional"`
	Code       string  `json:"code,optional"`
	Name       string  `json:"name,optional"`
	Pid        int64   `json:"pid,optional"`
	Sort       float64 `json:"sort,optional"`
	Status     int     `json:"status,optional"`
	UpdatedBy  int64   `json:"updatedBy"` // 更新人ID
}

type UpdateDictItemResp struct {
	Base
}
