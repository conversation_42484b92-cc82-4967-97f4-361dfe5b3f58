package logic

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"golang-docker-compose-main/golang/dict_category/api/internal/svc"
	"golang-docker-compose-main/golang/dict_category/api/internal/types"
)

type ListDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictCategoryLogic {
	return &ListDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictCategoryLogic) ListDictCategory(req *types.ListDictCategoryReq) (resp *types.ListDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
