package svc

import (
	"dict/rpc/internal/config"
	"gorm.io/gorm"
	"mp/rpc/model"
)

type ServiceContext struct {
	Config config.Config
	//DictModel model.DictModel
	DB *gorm.DB
}

func NewServiceContext(c config.Config) *ServiceContext {
	//mysqlConn := sqlx.NewMysql(c.MySql.DataSource)
	return &ServiceContext{
		Config: c,
		//DictModel: model.NewDictModel(mysqlConn, c.CacheRedis),
		DB: model.NewDb(c.MySql.DataSource),
	}
}
