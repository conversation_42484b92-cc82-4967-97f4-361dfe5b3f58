# 构建阶段
FROM golang:alpine AS builder

# 设置环境变量
ENV CGO_ENABLED 0
ENV GOOS linux
ENV GOPROXY https://goproxy.cn,direct

# 安装必要的软件包
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk --no-cache add tzdata

# 工作目录
WORKDIR /build

# 复制Go模块依赖文件
COPY go.mod .
COPY go.sum .
RUN go mod download

# 复制源代码
COPY . .

# 复制配置文件到指定位置
COPY rpc/etc /app/etc

# 编译应用
RUN go build -ldflags="-s -w" -o /app/dict_category rpc/dictcategory.go

# 运行阶段
FROM scratch

# 从builder阶段复制时区数据和SSL证书
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

# 设置环境变量
ENV TZ Asia/Shanghai

# 工作目录
WORKDIR /app

# 复制编译好的应用
COPY --from=builder /app/dict_category /app/dict_category
COPY --from=builder /app/etc /app/etc

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["./dict_category", "-f", "etc/dictcategory.yaml"] 