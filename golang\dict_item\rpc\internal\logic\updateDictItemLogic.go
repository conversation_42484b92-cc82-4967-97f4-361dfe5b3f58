package logic

import (
	"context"
	"database/sql"
	"time"

	"dict_item/rpc/dict_item"
	"dict_item/rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictItemLogic {
	return &UpdateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典项
func (l *UpdateDictItemLogic) UpdateDictItem(in *dict_item.UpdateDictItemReq) (*dict_item.UpdateDictItemResp, error) {
	// 先查询记录是否存在
	item, err := l.svcCtx.DictItemModel.FindOne(l.ctx, in.Id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	now := time.Now()

	// 设置可选字段
	if in.TenantId > 0 {
		item.TenantId = sql.NullInt64{Int64: in.TenantId, Valid: true}
	}
	if in.DictId > 0 {
		item.DictId = in.DictId
	}
	if in.CategoryId > 0 {
		item.CategoryId = sql.NullInt64{Int64: in.CategoryId, Valid: true}
	}
	if in.Code != "" {
		item.Code = in.Code
	}
	if in.Name != "" {
		item.Name = in.Name
	}
	if in.Pid > 0 {
		item.Pid = sql.NullInt64{Int64: in.Pid, Valid: true}
	}
	if in.Sort > 0 {
		item.Sort = in.Sort
	}
	if in.Status > 0 {
		item.Status = int64(in.Status)
	}
	if in.UpdatedBy > 0 {
		item.UpdatedBy = sql.NullInt64{Int64: in.UpdatedBy, Valid: true}
	}

	item.UpdatedTime = sql.NullTime{Time: now, Valid: true}

	// 执行更新
	err = l.svcCtx.DictItemModel.Update(l.ctx, item)
	if err != nil {
		return nil, err
	}

	return &dict_item.UpdateDictItemResp{
		Success: true,
	}, nil
}
